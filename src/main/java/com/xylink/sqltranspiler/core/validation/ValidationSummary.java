package com.xylink.sqltranspiler.core.validation;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;

/**
 * 验证结果摘要
 */
@Slf4j
public class ValidationSummary {
    
    private final int totalValidations;
    private final int conflictCount;
    private final Map<String, List<VarcharConflict>> conflictsByTable;
    private final long validationTimeMs;
    private final boolean enabled;
    
    public ValidationSummary(List<VarcharConflict> conflicts, int totalValidations, 
                           long validationTimeMs, boolean enabled) {
        this.totalValidations = totalValidations;
        this.conflictCount = conflicts.size();
        this.conflictsByTable = conflicts.stream()
            .collect(Collectors.groupingBy(VarcharConflict::getTableName));
        this.validationTimeMs = validationTimeMs;
        this.enabled = enabled;
    }
    
    /**
     * 创建空的验证摘要（用于禁用验证时）
     */
    public static ValidationSummary empty() {
        return new ValidationSummary(Collections.emptyList(), 0, 0, false);
    }
    
    /**
     * 创建禁用状态的摘要
     */
    public static ValidationSummary disabled() {
        return new ValidationSummary(Collections.emptyList(), 0, 0, false);
    }
    
    // Getter方法
    public int getTotalValidations() {
        return totalValidations;
    }
    
    public int getConflictCount() {
        return conflictCount;
    }
    
    public Map<String, List<VarcharConflict>> getConflictsByTable() {
        return Collections.unmodifiableMap(conflictsByTable);
    }
    
    public long getValidationTimeMs() {
        return validationTimeMs;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * 是否有冲突
     */
    public boolean hasConflicts() {
        return conflictCount > 0;
    }
    
    /**
     * 获取所有冲突列表
     */
    public List<VarcharConflict> getAllConflicts() {
        return conflictsByTable.values().stream()
            .flatMap(List::stream)
            .collect(Collectors.toList());
    }
    
    /**
     * 获取受影响的表数量
     */
    public int getAffectedTableCount() {
        return conflictsByTable.size();
    }
    
    /**
     * 打印详细报告
     */
    public void printReport() {
        if (!enabled) {
            log.info("VARCHAR Length Validation: DISABLED");
            return;
        }
        
        log.info("=== VARCHAR Length Validation Report ===");
        log.info("Total validations: {}", totalValidations);
        log.info("Conflicts found: {}", conflictCount);
        log.info("Affected tables: {}", getAffectedTableCount());
        log.info("Validation time: {} ms", validationTimeMs);

        if (conflictCount > 0) {
            log.info("Conflicts by table:");
            conflictsByTable.forEach((table, conflicts) -> {
                log.info("  {}: {} conflicts", table, conflicts.size());
                conflicts.forEach(conflict ->
                    log.info("    - {}", conflict.getShortDescription()));
            });

            log.info("Suggested actions:");
            log.info("1. Review and increase VARCHAR lengths in CREATE TABLE statements");
            log.info("2. Consider using TEXT/CLOB for unlimited length fields");
            log.info("3. Validate and possibly truncate input data");
        } else {
            log.info("✅ No VARCHAR length conflicts detected.");
        }
        log.info("==========================================");
    }
    
    /**
     * 获取简要报告字符串
     */
    public String getBriefReport() {
        if (!enabled) {
            return "VARCHAR validation: disabled";
        }
        
        if (conflictCount == 0) {
            return String.format("VARCHAR validation: %d checks, no conflicts (%d ms)", 
                totalValidations, validationTimeMs);
        } else {
            return String.format("VARCHAR validation: %d conflicts in %d tables (%d ms)", 
                conflictCount, getAffectedTableCount(), validationTimeMs);
        }
    }
    
    @Override
    public String toString() {
        return getBriefReport();
    }
}
