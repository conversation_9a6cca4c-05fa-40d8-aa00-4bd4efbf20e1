package com.xylink.sqltranspiler.infrastructure.parser.error;

import java.util.Set;

import org.antlr.v4.runtime.BaseErrorListener;
import org.antlr.v4.runtime.CharStreams;
import org.antlr.v4.runtime.CommonTokenStream;
import org.antlr.v4.runtime.ParserRuleContext;
import org.antlr.v4.runtime.RecognitionException;
import org.antlr.v4.runtime.Recognizer;
import org.antlr.v4.runtime.tree.ParseTree;

import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlLexer;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParser;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParserBaseVisitor;

import lombok.extern.slf4j.Slf4j;

/**
 * 基于AST的MySQL语法校验器
 * 
 * 架构设计原则：
 * 1. AST优先：首先进行完整的语法解析，构建抽象语法树
 * 2. 分层校验：在AST基础上进行各类语义校验
 * 3. 精确定位：基于语法树节点进行精确的错误定位
 * 4. 官方对标：严格按照MySQL 8.4官方语法规则进行校验
 * 
 * 相比原有混合架构的优势：
 * - 避免正则表达式的误判问题
 * - 提供更准确的错误定位
 * - 支持复杂语法结构的校验
 * - 便于扩展和维护
 * 
 * <AUTHOR> Transpiler Team
 */
@Slf4j
public class MySqlAstBasedValidator {

    /**
     * MySQL 8.4官方支持的函数白名单
     * 严格基于MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
     */
    private static final Set<String> MYSQL_OFFICIAL_FUNCTIONS = Set.of(
        // 数学函数 - https://dev.mysql.com/doc/refman/8.4/en/mathematical-functions.html
        "ABS", "ACOS", "ASIN", "ATAN", "ATAN2", "CEIL", "CEILING", "COS", "COT", 
        "DEGREES", "EXP", "FLOOR", "LN", "LOG", "LOG10", "LOG2", "MOD", "PI", 
        "POW", "POWER", "RADIANS", "RAND", "ROUND", "SIGN", "SIN", "SQRT", "TAN", "TRUNCATE",
        
        // 字符串函数 - https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
        "ASCII", "BIN", "BIT_LENGTH", "CHAR", "CHAR_LENGTH", "CHARACTER_LENGTH", 
        "CONCAT", "CONCAT_WS", "ELT", "EXPORT_SET", "FIELD", "FIND_IN_SET", "FORMAT", 
        "FROM_BASE64", "HEX", "INSERT", "INSTR", "LCASE", "LEFT", "LENGTH", "LOAD_FILE", 
        "LOCATE", "LOWER", "LPAD", "LTRIM", "MAKE_SET", "MATCH", "MID", "OCT", "ORD", 
        "POSITION", "QUOTE", "REPEAT", "REPLACE", "REVERSE", "RIGHT", "RPAD", "RTRIM", 
        "SOUNDEX", "SPACE", "STRCMP", "SUBSTR", "SUBSTRING", "SUBSTRING_INDEX", "TO_BASE64", 
        "TRIM", "UCASE", "UNHEX", "UPPER", "WEIGHT_STRING",
        
        // 日期时间函数 - https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
        "ADDDATE", "ADDTIME", "CONVERT_TZ", "CURDATE", "CURRENT_DATE", "CURRENT_TIME", 
        "CURRENT_TIMESTAMP", "CURTIME", "DATE", "DATE_ADD", "DATE_FORMAT", "DATE_SUB", 
        "DATEDIFF", "DAY", "DAYNAME", "DAYOFMONTH", "DAYOFWEEK", "DAYOFYEAR", "EXTRACT", 
        "FROM_DAYS", "FROM_UNIXTIME", "GET_FORMAT", "HOUR", "LAST_DAY", "LOCALTIME", 
        "LOCALTIMESTAMP", "MAKEDATE", "MAKETIME", "MICROSECOND", "MINUTE", "MONTH", 
        "MONTHNAME", "NOW", "PERIOD_ADD", "PERIOD_DIFF", "QUARTER", "SEC_TO_TIME", 
        "SECOND", "STR_TO_DATE", "SUBDATE", "SUBTIME", "SYSDATE", "TIME", "TIME_FORMAT", 
        "TIME_TO_SEC", "TIMEDIFF", "TIMESTAMP", "TIMESTAMPADD", "TIMESTAMPDIFF", 
        "TO_DAYS", "TO_SECONDS", "UNIX_TIMESTAMP", "UTC_DATE", "UTC_TIME", "UTC_TIMESTAMP", 
        "WEEK", "WEEKDAY", "WEEKOFYEAR", "YEAR", "YEARWEEK",
        
        // 聚合函数 - https://dev.mysql.com/doc/refman/8.4/en/aggregate-functions.html
        "AVG", "BIT_AND", "BIT_OR", "BIT_XOR", "COUNT", "GROUP_CONCAT", "JSON_ARRAYAGG", 
        "JSON_OBJECTAGG", "MAX", "MIN", "STD", "STDDEV", "STDDEV_POP", "STDDEV_SAMP", 
        "SUM", "VAR_POP", "VAR_SAMP", "VARIANCE",
        
        // 窗口函数 - https://dev.mysql.com/doc/refman/8.4/en/window-functions.html
        "CUME_DIST", "DENSE_RANK", "FIRST_VALUE", "LAG", "LAST_VALUE", "LEAD", 
        "NTH_VALUE", "NTILE", "PERCENT_RANK", "RANK", "ROW_NUMBER",
        
        // 控制流函数 - https://dev.mysql.com/doc/refman/8.4/en/flow-control-functions.html
        "CASE", "IF", "IFNULL", "NULLIF", "COALESCE",
        
        // 信息函数 - https://dev.mysql.com/doc/refman/8.4/en/information-functions.html
        "BENCHMARK", "CHARSET", "COERCIBILITY", "COLLATION", "CONNECTION_ID", "CURRENT_USER", 
        "DATABASE", "FOUND_ROWS", "LAST_INSERT_ID", "ROW_COUNT", "SCHEMA", "SESSION_USER", 
        "SYSTEM_USER", "USER", "VERSION",
        
        // JSON函数 - https://dev.mysql.com/doc/refman/8.4/en/json-functions.html
        "JSON_ARRAY", "JSON_ARRAY_APPEND", "JSON_ARRAY_INSERT", "JSON_CONTAINS", 
        "JSON_CONTAINS_PATH", "JSON_DEPTH", "JSON_EXTRACT", "JSON_INSERT", "JSON_KEYS", 
        "JSON_LENGTH", "JSON_MERGE", "JSON_MERGE_PATCH", "JSON_MERGE_PRESERVE", 
        "JSON_OBJECT", "JSON_OVERLAPS", "JSON_PRETTY", "JSON_QUOTE", "JSON_REMOVE", 
        "JSON_REPLACE", "JSON_SEARCH", "JSON_SET", "JSON_STORAGE_FREE", "JSON_STORAGE_SIZE", 
        "JSON_TABLE", "JSON_TYPE", "JSON_UNQUOTE", "JSON_VALID", "JSON_VALUE",
        
        // 加密和压缩函数 - https://dev.mysql.com/doc/refman/8.4/en/encryption-functions.html
        "AES_DECRYPT", "AES_ENCRYPT", "COMPRESS", "MD5", "RANDOM_BYTES", "SHA1", "SHA2", 
        "UNCOMPRESS", "UNCOMPRESSED_LENGTH", "VALIDATE_PASSWORD_STRENGTH",
        
        // 锁定函数 - https://dev.mysql.com/doc/refman/8.4/en/locking-functions.html
        "GET_LOCK", "IS_FREE_LOCK", "IS_USED_LOCK", "RELEASE_ALL_LOCKS", "RELEASE_LOCK",
        
        // 其他函数
        "CAST", "CONVERT", "DEFAULT", "INET_ATON", "INET_NTOA", "INET6_ATON", "INET6_NTOA", 
        "IS_IPV4", "IS_IPV4_COMPAT", "IS_IPV4_MAPPED", "IS_IPV6", "MASTER_POS_WAIT", 
        "NAME_CONST", "SLEEP", "UUID", "UUID_SHORT", "VALUES"
    );

    /**
     * AST优先的MySQL语法校验入口
     * 
     * 校验流程：
     * 1. 词法分析 → 2. 语法分析 → 3. AST构建 → 4. 语义校验
     * 
     * @param sql 待验证的SQL语句
     * @return 综合验证结果
     */
    public static MySqlSyntaxValidationResult validateWithAst(String sql) {
        MySqlSyntaxValidationResult result = new MySqlSyntaxValidationResult();
        
        try {
            // 第一步：构建AST
            ParseTree ast = buildAst(sql, result);
            if (ast == null) {
                // 语法解析失败，已在buildAst中记录错误
                return result;
            }
            
            // 第二步：基于AST进行语义校验
            performSemanticValidation(ast, result);
            
            log.debug("AST-based validation completed for SQL: {}", sql.substring(0, Math.min(sql.length(), 100)));
            
        } catch (Exception e) {
            log.error("AST-based validation failed", e);
            result.addViolation(new MySqlSyntaxViolation(
                "ast_validation_error",
                "AST校验异常",
                "语法树构建或校验过程中发生异常：" + e.getMessage(),
                "请检查SQL语法是否正确",
                SqlErrorType.GENERIC_SYNTAX_ERROR
            ));
        }
        
        return result;
    }
    
    /**
     * 构建抽象语法树
     * 
     * @param sql SQL语句
     * @param result 用于记录语法错误的结果对象
     * @return 语法树根节点，如果解析失败返回null
     */
    private static ParseTree buildAst(String sql, MySqlSyntaxValidationResult result) {
        try {
            // 1. 词法分析
            MySqlLexer lexer = new MySqlLexer(CharStreams.fromString(sql));
            lexer.removeErrorListeners();
            
            // 2. 语法分析
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            MySqlParser parser = new MySqlParser(tokens);
            parser.removeErrorListeners();
            
            // 3. 添加自定义错误监听器
            AstErrorListener errorListener = new AstErrorListener(result, sql);
            parser.addErrorListener(errorListener);
            
            // 4. 构建语法树
            ParseTree tree = parser.root();
            
            // 5. 检查是否有语法错误
            if (result.hasViolations()) {
                return null;
            }
            
            return tree;
            
        } catch (Exception e) {
            log.error("Failed to build AST", e);
            result.addViolation(new MySqlSyntaxViolation(
                "ast_build_error",
                "语法树构建失败",
                "无法构建抽象语法树：" + e.getMessage(),
                "请检查SQL语法是否符合MySQL规范",
                SqlErrorType.GENERIC_SYNTAX_ERROR
            ));
            return null;
        }
    }
    
    /**
     * 基于AST进行语义校验
     * 
     * @param ast 抽象语法树
     * @param result 校验结果
     */
    private static void performSemanticValidation(ParseTree ast, MySqlSyntaxValidationResult result) {
        // 创建语义校验访问者
        SemanticValidationVisitor visitor = new SemanticValidationVisitor(result);
        visitor.visit(ast);
    }
    
    /**
     * AST构建错误监听器
     */
    private static class AstErrorListener extends BaseErrorListener {
        private final MySqlSyntaxValidationResult result;
        private final String originalSql;

        public AstErrorListener(MySqlSyntaxValidationResult result) {
            this.result = result;
            this.originalSql = null;
        }

        public AstErrorListener(MySqlSyntaxValidationResult result, String originalSql) {
            this.result = result;
            this.originalSql = originalSql;
        }

        @Override
        public void syntaxError(Recognizer<?, ?> recognizer, Object offendingSymbol,
                              int line, int charPositionInLine, String msg, RecognitionException e) {

            // 使用增强的错误分析器生成详细的错误信息
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeSqlError(
                originalSql != null ? originalSql : "", msg, line, charPositionInLine);

            result.addViolation(new MySqlSyntaxViolation(
                "antlr_syntax_error",
                errorInfo.getFriendlyMessage(),
                errorInfo.getDetailedMessage(),
                errorInfo.getSuggestion(),
                errorInfo.getErrorType()
            ));
        }
    }
    
    /**
     * 语义校验访问者
     * 基于AST节点进行各类语义校验
     */
    private static class SemanticValidationVisitor extends MySqlParserBaseVisitor<Void> {
        
        private final MySqlSyntaxValidationResult result;
        
        public SemanticValidationVisitor(MySqlSyntaxValidationResult result) {
            this.result = result;
        }
        
        // 函数调用校验
        @Override
        public Void visitScalarFunctionCall(MySqlParser.ScalarFunctionCallContext ctx) {
            if (ctx.scalarFunctionName() != null) {
                String functionName = ctx.scalarFunctionName().getText().toUpperCase();
                validateFunctionName(functionName, ctx);
            }
            return super.visitScalarFunctionCall(ctx);
        }
        
        @Override
        public Void visitUdfFunctionCall(MySqlParser.UdfFunctionCallContext ctx) {
            if (ctx.fullId() != null) {
                String functionName = ctx.fullId().getText();
                // 只取最后一部分作为函数名
                String[] parts = functionName.split("\\.");
                String actualFunctionName = parts[parts.length - 1].toUpperCase();
                validateFunctionName(actualFunctionName, ctx);
            }
            return super.visitUdfFunctionCall(ctx);
        }
        
        // 可以继续添加其他语义校验...
        
        /**
         * 校验函数名是否在MySQL官方函数列表中
         */
        private void validateFunctionName(String functionName, ParserRuleContext ctx) {
            if (!MYSQL_OFFICIAL_FUNCTIONS.contains(functionName)) {
                result.addViolation(new MySqlSyntaxViolation(
                    "unknown_function",
                    "未知函数",
                    String.format("函数 '%s' 不在MySQL 8.4官方函数列表中", functionName),
                    "请确认函数名称是否正确，或参考MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/built-in-function-reference.html",
                    SqlErrorType.FUNCTION_NAME_ERROR
                ));
                log.debug("Unknown function detected: {} at line {}", functionName, ctx.getStart().getLine());
            }
        }
    }
}
