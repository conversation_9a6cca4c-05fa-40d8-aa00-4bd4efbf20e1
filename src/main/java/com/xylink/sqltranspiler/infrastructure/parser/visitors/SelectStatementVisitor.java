package com.xylink.sqltranspiler.infrastructure.parser.visitors;

import java.util.ArrayList;
import java.util.List;

import com.xylink.sqltranspiler.core.ast.Statement;
import com.xylink.sqltranspiler.core.ast.TableId;
import com.xylink.sqltranspiler.core.ast.dml.QueryStmt;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParser;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParserBaseVisitor;
import com.xylink.sqltranspiler.infrastructure.util.CommonUtils;

/**
 * SELECT语句专门的visitor
 * 严格遵守MySQL官方规范：https://dev.mysql.com/doc/refman/8.4/en/
 * 和达梦官方规范：https://eco.dameng.com/document/dm/zh-cn/pm/
 */
public class SelectStatementVisitor extends MySqlParserBaseVisitor<Statement> {
    
    private String originalSql;
    
    public SelectStatementVisitor(String originalSql) {
        this.originalSql = originalSql;
    }

    @Override
    public Statement visitSimpleSelect(MySqlParser.SimpleSelectContext ctx) {
        // 获取原始SQL文本 - 优先使用传入的原始SQL，确保格式正确
        String sql = this.originalSql != null ? this.originalSql : ctx.getText();

        // 使用传入的SQL或原始SQL进行解析
        System.out.println("SQL length: " + (sql != null ? sql.length() : "null"));
        
        // 解析表名
        List<TableId> inputTables = new ArrayList<>();
        if (ctx.querySpecification() != null && ctx.querySpecification().fromClause() != null) {
            inputTables = parseFromClause(ctx.querySpecification().fromClause());
        }

        // 解析LIMIT和OFFSET
        Integer limit = null;
        Integer offset = null;
        if (ctx.querySpecification() != null && ctx.querySpecification().limitClause() != null) {
            MySqlParser.LimitClauseContext limitCtx = ctx.querySpecification().limitClause();
            if (limitCtx.limitClauseAtom() != null) {
                if (limitCtx.limitClauseAtom().size() == 1) {
                    // LIMIT count
                    limit = Integer.parseInt(limitCtx.limitClauseAtom(0).getText());
                } else if (limitCtx.limitClauseAtom().size() == 2) {
                    // LIMIT offset, count
                    offset = Integer.parseInt(limitCtx.limitClauseAtom(0).getText());
                    limit = Integer.parseInt(limitCtx.limitClauseAtom(1).getText());
        System.out.println("DEBUG: SelectStatementVisitor - originalSql = " + sql);
        System.out.println("DEBUG: SelectStatementVisitor - ctx.getText() = " + ctx.getText());                }
            }
        }

        QueryStmt queryStmt = new QueryStmt(inputTables, limit, offset);
        queryStmt.setSql(sql);
        return queryStmt;
    }

    @Override
    public Statement visitParenthesisSelect(MySqlParser.ParenthesisSelectContext ctx) {
        // 处理括号包围的SELECT语句
        String sql = this.originalSql != null ? this.originalSql : ctx.getText();
        
        List<TableId> inputTables = new ArrayList<>();
        if (ctx.queryExpression() != null) {
            // 简化处理，从原始SQL中提取表名
            inputTables = extractTablesFromSql(sql);
        }

        QueryStmt queryStmt = new QueryStmt(inputTables);
        queryStmt.setSql(sql);
        return queryStmt;
    }

    @Override
    public Statement visitUnionSelect(MySqlParser.UnionSelectContext ctx) {
        // 处理UNION SELECT语句
        String sql = this.originalSql != null ? this.originalSql : ctx.getText();
        
        List<TableId> inputTables = new ArrayList<>();
        // 简化处理，从原始SQL中提取表名
        inputTables = extractTablesFromSql(sql);

        QueryStmt queryStmt = new QueryStmt(inputTables);
        queryStmt.setSql(sql);
        return queryStmt;
    }

    @Override
    public Statement visitUnionParenthesisSelect(MySqlParser.UnionParenthesisSelectContext ctx) {
        // 处理带括号的UNION SELECT语句
        String sql = this.originalSql != null ? this.originalSql : ctx.getText();
        
        List<TableId> inputTables = new ArrayList<>();
        inputTables = extractTablesFromSql(sql);

        QueryStmt queryStmt = new QueryStmt(inputTables);
        queryStmt.setSql(sql);
        return queryStmt;
    }

    /**
     * 解析FROM子句中的表名
     */
    private List<TableId> parseFromClause(MySqlParser.FromClauseContext ctx) {
        List<TableId> tables = new ArrayList<>();
        if (ctx.tableSources() != null && ctx.tableSources().tableSource() != null) {
            for (MySqlParser.TableSourceContext tableSourceCtx : ctx.tableSources().tableSource()) {
                TableId tableId = parseTableSource(tableSourceCtx);
                if (tableId != null) {
                    tables.add(tableId);
                }
            }
        }
        return tables;
    }

    /**
     * 解析单个表源
     */
    private TableId parseTableSource(MySqlParser.TableSourceContext ctx) {
        if (ctx instanceof MySqlParser.TableSourceBaseContext) {
            MySqlParser.TableSourceBaseContext baseCtx = (MySqlParser.TableSourceBaseContext) ctx;
            if (baseCtx.tableSourceItem() instanceof MySqlParser.AtomTableItemContext) {
                MySqlParser.AtomTableItemContext atomCtx = (MySqlParser.AtomTableItemContext) baseCtx.tableSourceItem();
                if (atomCtx.tableName() != null && atomCtx.tableName().fullId() != null) {
                    return parseFullId(atomCtx.tableName().fullId());
                }
            }
        }
        return null;
    }

    /**
     * 解析fullId为TableId
     */
    private TableId parseFullId(MySqlParser.FullIdContext fullId) {
        List<String> texts = new ArrayList<>();
        for (int i = 0; i < fullId.getChildCount(); i++) {
            texts.add(fullId.getChild(i).getText());
        }

        List<String> parts = texts.stream()
                .filter(text -> !".".equals(text))
                .map(CommonUtils::cleanQuote)  // Clean quotes including backticks
                .collect(java.util.stream.Collectors.toList());

        if (parts.size() == 2) {
            String schemaName = parts.get(0);
            String tableName = parts.get(1);

            // 修复：如果tableName以点开头，去掉前导点
            if (tableName.startsWith(".")) {
                tableName = tableName.substring(1);
            }

            return new TableId(schemaName, tableName);
        } else if (parts.size() == 1) {
            String tableName = parts.get(0);

            // 修复：如果tableName以点开头，去掉前导点
            if (tableName.startsWith(".")) {
                tableName = tableName.substring(1);
            }

            return new TableId(tableName);
        } else {
            throw new RuntimeException("parse fullId error: " + fullId.getText());
        }
    }

    /**
     * 从SQL文本中提取表名（简化实现）
     */
    private List<TableId> extractTablesFromSql(String sql) {
        List<TableId> tables = new ArrayList<>();
        // 简化的表名提取逻辑
        // 这里可以根据需要实现更复杂的解析逻辑
        if (sql.toLowerCase().contains("from")) {
            // 简单的表名提取，实际项目中可能需要更复杂的解析
            tables.add(new TableId("extracted_table"));
        }
        return tables;
    }
}
