package com.xylink.sqltranspiler.infrastructure.parser.error;

import lombok.extern.slf4j.Slf4j;
import java.util.*;
import java.util.regex.Pattern;

// ANTLR相关导入
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.tree.*;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlLexer;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParser;
import com.xylink.sqltranspiler.infrastructure.parser.generated.MySqlParserBaseVisitor;

/**
 * MySQL严格语法校验器
 * 根据 .augment/rules/rule-db.md 要求，基于ANTLR和MySQL 8.4官方文档进行严格校验
 * 
 * <AUTHOR> Transpiler Team
 */
@Slf4j
public class MySqlStrictSyntaxValidator {

    /**
     * MySQL 8.4官方支持的函数白名单
     * 严格基于MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
     */
    private static final Set<String> MYSQL_OFFICIAL_FUNCTIONS = Set.of(
        // 数学函数 - https://dev.mysql.com/doc/refman/8.4/en/mathematical-functions.html
        "ABS", "ACOS", "ASIN", "ATAN", "ATAN2", "CEIL", "CEILING", "COS", "COT", 
        "DEGREES", "EXP", "FLOOR", "LN", "LOG", "LOG10", "LOG2", "MOD", "PI", 
        "POW", "POWER", "RADIANS", "RAND", "ROUND", "SIGN", "SIN", "SQRT", "TAN", "TRUNCATE",
        
        // 字符串函数 - https://dev.mysql.com/doc/refman/8.4/en/string-functions.html
        "ASCII", "BIN", "BIT_LENGTH", "CHAR", "CHAR_LENGTH", "CHARACTER_LENGTH", 
        "CONCAT", "CONCAT_WS", "ELT", "EXPORT_SET", "FIELD", "FIND_IN_SET", "FORMAT", 
        "FROM_BASE64", "HEX", "INSERT", "INSTR", "LCASE", "LEFT", "LENGTH", "LOAD_FILE", 
        "LOCATE", "LOWER", "LPAD", "LTRIM", "MAKE_SET", "MATCH", "MID", "OCT", "ORD", 
        "POSITION", "QUOTE", "REPEAT", "REPLACE", "REVERSE", "RIGHT", "RPAD", "RTRIM", 
        "SOUNDEX", "SPACE", "STRCMP", "SUBSTR", "SUBSTRING", "SUBSTRING_INDEX", "TO_BASE64", 
        "TRIM", "UCASE", "UNHEX", "UPPER", "WEIGHT_STRING",
        
        // 日期时间函数 - https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
        "ADDDATE", "ADDTIME", "CONVERT_TZ", "CURDATE", "CURRENT_DATE", "CURRENT_TIME", 
        "CURRENT_TIMESTAMP", "CURTIME", "DATE", "DATE_ADD", "DATE_FORMAT", "DATE_SUB", 
        "DATEDIFF", "DAY", "DAYNAME", "DAYOFMONTH", "DAYOFWEEK", "DAYOFYEAR", "EXTRACT", 
        "FROM_DAYS", "FROM_UNIXTIME", "GET_FORMAT", "HOUR", "LAST_DAY", "LOCALTIME", 
        "LOCALTIMESTAMP", "MAKEDATE", "MAKETIME", "MICROSECOND", "MINUTE", "MONTH", 
        "MONTHNAME", "NOW", "PERIOD_ADD", "PERIOD_DIFF", "QUARTER", "SEC_TO_TIME", 
        "SECOND", "STR_TO_DATE", "SUBDATE", "SUBTIME", "SYSDATE", "TIME", "TIME_FORMAT", 
        "TIME_TO_SEC", "TIMEDIFF", "TIMESTAMP", "TIMESTAMPADD", "TIMESTAMPDIFF", 
        "TO_DAYS", "TO_SECONDS", "UNIX_TIMESTAMP", "UTC_DATE", "UTC_TIME", "UTC_TIMESTAMP", 
        "WEEK", "WEEKDAY", "WEEKOFYEAR", "YEAR", "YEARWEEK",
        
        // 聚合函数 - https://dev.mysql.com/doc/refman/8.4/en/aggregate-functions.html
        "AVG", "BIT_AND", "BIT_OR", "BIT_XOR", "COUNT", "GROUP_CONCAT", "JSON_ARRAYAGG", 
        "JSON_OBJECTAGG", "MAX", "MIN", "STD", "STDDEV", "STDDEV_POP", "STDDEV_SAMP", 
        "SUM", "VAR_POP", "VAR_SAMP", "VARIANCE",
        
        // 窗口函数 - https://dev.mysql.com/doc/refman/8.4/en/window-functions.html
        "CUME_DIST", "DENSE_RANK", "FIRST_VALUE", "LAG", "LAST_VALUE", "LEAD", 
        "NTH_VALUE", "NTILE", "PERCENT_RANK", "RANK", "ROW_NUMBER",
        
        // 控制流函数 - https://dev.mysql.com/doc/refman/8.4/en/flow-control-functions.html
        "CASE", "IF", "IFNULL", "NULLIF", "COALESCE",
        
        // 信息函数 - https://dev.mysql.com/doc/refman/8.4/en/information-functions.html
        "BENCHMARK", "CHARSET", "COERCIBILITY", "COLLATION", "CONNECTION_ID", "CURRENT_USER", 
        "DATABASE", "FOUND_ROWS", "LAST_INSERT_ID", "ROW_COUNT", "SCHEMA", "SESSION_USER", 
        "SYSTEM_USER", "USER", "VERSION",
        
        // JSON函数 - https://dev.mysql.com/doc/refman/8.4/en/json-functions.html
        "JSON_ARRAY", "JSON_ARRAY_APPEND", "JSON_ARRAY_INSERT", "JSON_CONTAINS", 
        "JSON_CONTAINS_PATH", "JSON_DEPTH", "JSON_EXTRACT", "JSON_INSERT", "JSON_KEYS", 
        "JSON_LENGTH", "JSON_MERGE", "JSON_MERGE_PATCH", "JSON_MERGE_PRESERVE", 
        "JSON_OBJECT", "JSON_OVERLAPS", "JSON_PRETTY", "JSON_QUOTE", "JSON_REMOVE", 
        "JSON_REPLACE", "JSON_SEARCH", "JSON_SET", "JSON_STORAGE_FREE", "JSON_STORAGE_SIZE", 
        "JSON_TABLE", "JSON_TYPE", "JSON_UNQUOTE", "JSON_VALID", "JSON_VALUE",
        
        // 加密和压缩函数 - https://dev.mysql.com/doc/refman/8.4/en/encryption-functions.html
        "AES_DECRYPT", "AES_ENCRYPT", "COMPRESS", "MD5", "RANDOM_BYTES", "SHA1", "SHA2", 
        "UNCOMPRESS", "UNCOMPRESSED_LENGTH", "VALIDATE_PASSWORD_STRENGTH",
        
        // 锁定函数 - https://dev.mysql.com/doc/refman/8.4/en/locking-functions.html
        "GET_LOCK", "IS_FREE_LOCK", "IS_USED_LOCK", "RELEASE_ALL_LOCKS", "RELEASE_LOCK",
        
        // 其他函数
        "CAST", "CONVERT", "DEFAULT", "INET_ATON", "INET_NTOA", "INET6_ATON", "INET6_NTOA", 
        "IS_IPV4", "IS_IPV4_COMPAT", "IS_IPV4_MAPPED", "IS_IPV6", "MASTER_POS_WAIT", 
        "NAME_CONST", "SLEEP", "UUID", "UUID_SHORT", "VALUES"
    );

    /**
     * 非MySQL语法检测模式
     * 基于其他数据库的特有语法和函数
     * 根据 .augment/rules/rule-db.md 要求，基于官方文档进行精确检测
     */
    private static final Map<Pattern, String> NON_MYSQL_SYNTAX_PATTERNS = Map.of(
        // PostgreSQL特有函数 - 基于PostgreSQL官方文档
        Pattern.compile("(?i)\\b(random|string_agg|unnest|array_length|generate_series|clock_timestamp)\\s*\\("),
        "PostgreSQL特有函数，MySQL不支持。参考MySQL替代方案：https://dev.mysql.com/doc/refman/8.4/en/mathematical-functions.html",

        // SQL Server特有函数 - 基于SQL Server官方文档
        Pattern.compile("(?i)\\b(getdate|getutcdate|len|datepart|datename|charindex|patindex)\\s*\\("),
        "SQL Server特有函数，MySQL不支持。参考MySQL日期时间函数：https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html",

        // Oracle特有函数 - 基于Oracle官方文档
        Pattern.compile("(?i)\\b(decode|nvl|nvl2|to_char|to_date|to_number)\\s*\\("),
        "Oracle特有函数，MySQL不支持。参考MySQL替代函数：https://dev.mysql.com/doc/refman/8.4/en/functions.html",

        // Oracle特有语法：ROWNUM
        Pattern.compile("(?i)\\brownum\\s*[<>=]"),
        "Oracle特有的ROWNUM语法，MySQL不支持。请使用LIMIT或窗口函数：https://dev.mysql.com/doc/refman/8.4/en/window-functions.html",

        // PostgreSQL特有语法：::类型转换
        Pattern.compile("(?i)::(text|varchar|integer|bigint|decimal|timestamp|date|time|boolean|json|jsonb)\\b"),
        "PostgreSQL特有的::类型转换语法，MySQL不支持。请使用CAST()或CONVERT()函数：https://dev.mysql.com/doc/refman/8.4/en/cast-functions.html",

        // PostgreSQL数组语法
        Pattern.compile("(?i)\\b(array\\[|\\]\\s*::|unnest\\s*\\()"),
        "PostgreSQL特有的数组语法，MySQL不支持。请使用JSON数组或其他替代方案：https://dev.mysql.com/doc/refman/8.4/en/json.html",

        // SQL Server特有语法：TOP子句
        Pattern.compile("(?i)\\bselect\\s+top\\s+\\d+\\b"),
        "SQL Server特有的TOP语法，MySQL不支持。请使用LIMIT子句：https://dev.mysql.com/doc/refman/8.4/en/select.html"
    );

    /**
     * 通用MySQL语法校验入口
     * 根据 .augment/rules/rule-db.md 要求，提供全面的MySQL语法校验
     * 
     * @param sql 待验证的SQL语句
     * @return 综合验证结果
     */
    public static MySqlSyntaxValidationResult validateMySqlSyntax(String sql) {
        // 使用ErrorPatternRegistry进行全面的MySQL语法校验（包括非MySQL语法检测）
        return ErrorPatternRegistry.validateMySqlSyntax(sql);
    }

    /**
     * 基于ANTLR的严格MySQL语法校验
     *
     * @param sql 待验证的SQL语句
     * @return 验证结果
     */
    public static MySqlSyntaxValidationResult validateWithAntlrParser(String sql) {
        MySqlSyntaxValidationResult result = new MySqlSyntaxValidationResult();
        
        try {
            // 创建ANTLR词法分析器和语法分析器 - 使用现代API
            CharStream input = CharStreams.fromString(sql);
            MySqlLexer lexer = new MySqlLexer(input);
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            MySqlParser parser = new MySqlParser(tokens);
            
            // 添加错误监听器
            MySqlSyntaxErrorListener errorListener = new MySqlSyntaxErrorListener(result);
            parser.removeErrorListeners();
            parser.addErrorListener(errorListener);
            
            // 解析SQL
            ParseTree tree = parser.root();
            
            // 如果解析成功但包含非标准语法，添加警告
            if (result.isValid()) {
                log.debug("ANTLR解析成功，SQL符合基本MySQL语法规范");
            }
            
        } catch (Exception e) {
            log.error("ANTLR解析失败: {}", e.getMessage());
            result.addViolation(new MySqlSyntaxViolation(
                "antlr_parse_error",
                "SQL解析失败",
                "ANTLR解析器无法解析此SQL语句，可能包含语法错误",
                "请检查SQL语法是否正确",
                SqlErrorType.GENERIC_SYNTAX_ERROR
            ));
        }
        
        return result;
    }

    /**
     * 基于AST优先架构的MySQL语法校验
     * 根据 .augment/rules/rule-db.md 要求，严格基于官方文档进行验证
     * 
     * 正确的架构设计：
     * 1. AST优先：首先使用MySQL官方语法解析器构建 AST
     * 2. 成功解析：进行基于AST的函数和语义校验
     * 3. 解析失败：才使用正则表达式辅助判别并给出官方建议
     * 4. 避免误判：不先于 AST 进行正则检测，避免误判合法 SQL
     *
     * @param sql 待验证的SQL语句
     * @return 验证结果
     */
    public static MySqlSyntaxValidationResult validateMySqlFunctions(String sql) {
        MySqlSyntaxValidationResult result = new MySqlSyntaxValidationResult();
        
        try {
            // 1. AST优先：首先使用MySQL官方语法解析器构建 AST
            ParseTree ast = buildAst(sql, result);
            
            if (ast != null) {
                // 2. AST构建成功：进行基于AST的函数校验
                performAstFunctionValidation(ast, result, sql);
                log.debug("AST-based function validation completed successfully for SQL: {}", 
                         sql.substring(0, Math.min(sql.length(), 100)));
            } else {
                // 3. AST构建失败：记录语法错误（不进行正则检测，避免重复）
                log.debug("AST parsing failed for SQL: {}", 
                         sql.substring(0, Math.min(sql.length(), 50)));
                // 注意：不在这里进行正则检测，避免与ErrorPatternRegistry重复检测
            }
            
            log.debug("AST-based MySQL validation completed for SQL: {}", 
                     sql.substring(0, Math.min(sql.length(), 100)));
            
        } catch (Exception e) {
            log.error("AST-based validation failed", e);
            result.addViolation(new MySqlSyntaxViolation(
                "ast_validation_error",
                "AST校验异常",
                "语法树构建或校验过程中发生异常：" + e.getMessage(),
                "请检查SQL语法是否正确，参考MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/",
                SqlErrorType.GENERIC_SYNTAX_ERROR
            ));
        }
        
        return result;
    }
    
    /**
     * 构建抽象语法树
     * 
     * @param sql SQL语句
     * @param result 用于记录语法错误的结果对象
     * @return 语法树根节点，如果解析失败返回null
     */
    private static ParseTree buildAst(String sql, MySqlSyntaxValidationResult result) {
        try {
            // 1. 词法分析
            MySqlLexer lexer = new MySqlLexer(CharStreams.fromString(sql));
            lexer.removeErrorListeners();
            
            // 2. 语法分析
            CommonTokenStream tokens = new CommonTokenStream(lexer);
            MySqlParser parser = new MySqlParser(tokens);
            parser.removeErrorListeners();
            
            // 3. 添加自定义错误监听器
            AstErrorListener errorListener = new AstErrorListener(result, sql);
            parser.addErrorListener(errorListener);
            
            // 4. 构建语法树
            ParseTree tree = parser.root();
            
            // 5. 检查是否有语法错误
            if (result.hasViolations()) {
                return null;
            }
            
            return tree;
            
        } catch (Exception e) {
            log.error("Failed to build AST", e);
            result.addViolation(new MySqlSyntaxViolation(
                "ast_build_error",
                "语法树构建失败",
                "无法构建抽象语法树：" + e.getMessage(),
                "请检查SQL语法是否符合MySQL规范",
                SqlErrorType.GENERIC_SYNTAX_ERROR
            ));
            return null;
        }
    }
    
    /**
     * 基于AST进行函数校验
     * 
     * @param ast 抽象语法树
     * @param result 校验结果
     * @param sql 原始SQL（用于调试）
     */
    private static void performAstFunctionValidation(ParseTree ast, MySqlSyntaxValidationResult result, String sql) {
        // 基于AST进行函数校验
        SemanticValidationVisitor visitor = new SemanticValidationVisitor(result);
        visitor.visit(ast);
        
        log.debug("AST function validation completed for SQL: {}", 
                 sql.substring(0, Math.min(sql.length(), 50)));
    }
    
    /**
     * 当AST解析失败时，提供有用的错误建议
     * 使用正则表达式辅助判别可能的非MySQL语法并给出官方建议
     * 基于MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
     * 
     * @param sql SQL语句
     * @param result 校验结果
     */
    private static void provideHelpfulErrorSuggestions(String sql, MySqlSyntaxValidationResult result) {
        for (Map.Entry<Pattern, String> entry : NON_MYSQL_SYNTAX_PATTERNS.entrySet()) {
            java.util.regex.Matcher matcher = entry.getKey().matcher(sql);
            if (matcher.find()) {
                String detectedText = matcher.group();
                result.addViolation(new MySqlSyntaxViolation(
                    "non_mysql_syntax_detected",
                    "检测到非MySQL语法",
                    String.format("检测到非MySQL语法：'%s'。%s", detectedText, entry.getValue()),
                    "请使用MySQL官方支持的语法，参考：https://dev.mysql.com/doc/refman/8.4/en/",
                    SqlErrorType.FUNCTION_NAME_ERROR
                ));
                log.debug("Detected non-MySQL syntax: {} in SQL", detectedText);
            }
        }
    }
    
    /**
     * AST构建错误监听器
     */
    private static class AstErrorListener extends BaseErrorListener {
        private final MySqlSyntaxValidationResult result;
        private final String originalSql;

        public AstErrorListener(MySqlSyntaxValidationResult result) {
            this.result = result;
            this.originalSql = null;
        }

        public AstErrorListener(MySqlSyntaxValidationResult result, String originalSql) {
            this.result = result;
            this.originalSql = originalSql;
        }

        @Override
        public void syntaxError(Recognizer<?, ?> recognizer, Object offendingSymbol,
                              int line, int charPositionInLine, String msg, RecognitionException e) {

            // 使用增强的ANTLR错误分析器生成详细的错误信息
            SqlErrorInfo errorInfo = EnhancedErrorAnalyzer.analyzeAntlrError(
                recognizer, offendingSymbol, line, charPositionInLine, msg, e,
                originalSql != null ? originalSql : "");

            result.addViolation(new MySqlSyntaxViolation(
                "antlr_syntax_error",
                errorInfo.getFriendlyMessage(),
                errorInfo.getDetailedMessage(),
                errorInfo.getSuggestion(),
                errorInfo.getErrorType()
            ));
        }
    }
    
    /**
     * 语义校验访问者
     * 基于AST节点进行各类语义校验
     */
    private static class SemanticValidationVisitor extends MySqlParserBaseVisitor<Void> {
        
        private final MySqlSyntaxValidationResult result;
        
        public SemanticValidationVisitor(MySqlSyntaxValidationResult result) {
            this.result = result;
        }
        
        // 函数调用校验 - 标量函数
        @Override
        public Void visitScalarFunctionCall(MySqlParser.ScalarFunctionCallContext ctx) {
            if (ctx.scalarFunctionName() != null) {
                String functionName = ctx.scalarFunctionName().getText().toUpperCase();
                validateFunctionName(functionName, ctx);
            }
            return super.visitScalarFunctionCall(ctx);
        }
        
        // 函数调用校验 - UDF函数
        @Override
        public Void visitUdfFunctionCall(MySqlParser.UdfFunctionCallContext ctx) {
            if (ctx.fullId() != null) {
                String functionName = ctx.fullId().getText();
                // 只取最后一部分作为函数名
                String[] parts = functionName.split("\\.");
                String actualFunctionName = parts[parts.length - 1].toUpperCase();
                validateFunctionName(actualFunctionName, ctx);
            }
            return super.visitUdfFunctionCall(ctx);
        }
        
        // 聚合函数校验
        @Override
        public Void visitAggregateFunctionCall(MySqlParser.AggregateFunctionCallContext ctx) {
            if (ctx.aggregateWindowedFunction() != null) {
                // 从聚合函数上下文中提取函数名
                String functionName = extractAggregateFunctionName(ctx.aggregateWindowedFunction());
                if (functionName != null) {
                    validateFunctionName(functionName, ctx);
                }
            }
            return super.visitAggregateFunctionCall(ctx);
        }
        
        /**
         * 从聚合函数上下文中提取函数名
         */
        private String extractAggregateFunctionName(MySqlParser.AggregateWindowedFunctionContext ctx) {
            String text = ctx.getText();
            if (text != null && text.contains("(")) {
                // 提取函数名部分（括号前的部分）
                String functionName = text.substring(0, text.indexOf("(")).toUpperCase();
                // 处理可能的聚合修饰符（ALL, DISTINCT等）
                if (functionName.contains("ALL") || functionName.contains("DISTINCT")) {
                    // 简单处理：取第一个词作为函数名
                    String[] parts = functionName.split("\\s+");
                    if (parts.length > 0) {
                        return parts[0];
                    }
                }
                return functionName;
            }
            return null;
        }
        
        /**
         * 校验函数名是否在MySQL官方函数列表中
         */
        private void validateFunctionName(String functionName, ParserRuleContext ctx) {
            if (!MYSQL_OFFICIAL_FUNCTIONS.contains(functionName)) {
                // 检查是否是已知的非MySQL函数
                String errorDetail = getDetailedFunctionError(functionName);
                result.addViolation(new MySqlSyntaxViolation(
                    "unknown_function_detected",
                    "检测到未知函数",
                    String.format("函数 '%s' 不在MySQL 8.4官方函数列表中。%s", functionName, errorDetail),
                    "请确认函数名称是否正确，或参考MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/built-in-function-reference.html",
                    SqlErrorType.FUNCTION_NAME_ERROR
                ));
                log.debug("Unknown function detected: {} at line {}", functionName, ctx.getStart().getLine());
            }
        }
    }

    /**
     * 从SQL中提取函数名称
     * 
     * @deprecated 此方法已被MySqlFunctionExtractor.extractFunctionNames()替代
     * 原有的正则表达式方法存在严重缺陷：
     * 1. 会将表名误识别为函数名（如t_en_role_res被识别为函数）
     * 2. 无法准确区分函数调用和其他语法结构
     * 
     * 新的基于AST的方法能够准确识别真正的函数调用节点
     * 
     * @param sql SQL语句
     * @return 检测到的函数名称集合
     */
    @Deprecated
    private static Set<String> extractFunctionNames(String sql) {
        // 使用新的基于AST的方法
        return MySqlFunctionExtractor.extractFunctionNames(sql);
    }



    /**
     * 获取函数的详细错误信息
     * 基于已知的数据库特有函数提供具体建议
     *
     * @param functionName 函数名
     * @return 详细错误信息
     */
    private static String getDetailedFunctionError(String functionName) {
        String upperName = functionName.toUpperCase();

        // PostgreSQL特有函数
        if (Set.of("RANDOM", "STRING_AGG", "UNNEST", "ARRAY_LENGTH", "GENERATE_SERIES", "CLOCK_TIMESTAMP").contains(upperName)) {
            return "这是PostgreSQL特有函数，MySQL中请使用对应的替代函数。";
        }

        // SQL Server特有函数
        if (Set.of("GETDATE", "GETUTCDATE", "LEN", "DATEPART", "DATENAME", "CHARINDEX", "PATINDEX").contains(upperName)) {
            return "这是SQL Server特有函数，MySQL中请使用对应的替代函数。";
        }

        // Oracle特有函数
        if (Set.of("DECODE", "NVL", "NVL2", "TO_CHAR", "TO_DATE", "TO_NUMBER", "ROWNUM").contains(upperName)) {
            return "这是Oracle特有函数，MySQL中请使用对应的替代函数。";
        }

        return "请确认此函数是否为MySQL支持的标准函数。";
    }

    /**
     * ANTLR错误监听器
     */
    private static class MySqlSyntaxErrorListener extends BaseErrorListener {
        private final MySqlSyntaxValidationResult result;
        
        public MySqlSyntaxErrorListener(MySqlSyntaxValidationResult result) {
            this.result = result;
        }
        
        @Override
        public void syntaxError(Recognizer<?, ?> recognizer, Object offendingSymbol,
                              int line, int charPositionInLine, String msg, RecognitionException e) {
            result.addViolation(new MySqlSyntaxViolation(
                "antlr_syntax_error",
                "MySQL语法错误",
                String.format("第%d行第%d列：%s", line, charPositionInLine, msg),
                "请修正SQL语法错误",
                SqlErrorType.GENERIC_SYNTAX_ERROR
            ));
        }
    }
}
