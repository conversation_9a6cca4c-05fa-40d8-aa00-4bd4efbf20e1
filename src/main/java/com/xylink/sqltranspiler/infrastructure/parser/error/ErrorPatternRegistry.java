package com.xylink.sqltranspiler.infrastructure.parser.error;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.regex.Pattern;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 错误模式注册表
 * 提供可配置的错误模式匹配和处理
 */
@Slf4j
public class ErrorPatternRegistry {
    
    private static final List<ErrorPattern> ERROR_PATTERNS = new ArrayList<>();
    
    static {
        // 注册神通数据库特定错误模式（优先级最高）
        registerShentongPatterns();

        // 注册MySQL特定错误模式
        registerMySqlPatterns();

        // 注册通用ANTLR错误模式
        registerAntlrPatterns();

        // 注册SQL通用错误模式（包含非MySQL语法检测）
        registerSqlPatterns();

        // 注册数据库转译特定错误模式
        registerTranspilationPatterns();

        // 注册MySQL 8.4特定错误模式
        registerMySql84Patterns();

        // 注册高级SQL特性错误模式
        registerAdvancedSqlPatterns();
    }
    
    /**
     * 注册MySQL特定错误模式
     */
    private static void registerMySqlPatterns() {
        // ON UPDATE NOW() 错误
        register(new ErrorPattern(
            "mysql_on_update_now",
            Pattern.compile("(?i)\\bON\\s+UPDATE\\s+NOW\\s*\\(\\s*\\)"),
            SqlErrorType.INVALID_ON_UPDATE_SYNTAX,
            "语法错误：ON UPDATE 子句中不能使用 NOW() 函数",
            "在 MySQL 中，ON UPDATE 子句只能使用 CURRENT_TIMESTAMP，不能使用 NOW() 函数。虽然 NOW() 是 CURRENT_TIMESTAMP 的同义词，但在 ON UPDATE 子句中有特定的语法限制。",
            "将 'ON UPDATE NOW()' 改为 'ON UPDATE CURRENT_TIMESTAMP'",
            true,
            sql -> sql.replaceAll("(?i)\\bON\\s+UPDATE\\s+NOW\\s*\\(\\s*\\)", "ON UPDATE CURRENT_TIMESTAMP")
        ));

        // DEFAULT NOW() 建议
        register(new ErrorPattern(
            "mysql_default_now",
            Pattern.compile("(?i)\\bDEFAULT\\s+NOW\\s*\\(\\s*\\)"),
            SqlErrorType.INVALID_DEFAULT_SYNTAX,
            "建议：DEFAULT 子句建议使用 CURRENT_TIMESTAMP",
            "虽然 DEFAULT NOW() 在某些情况下可能有效，但建议使用标准的 CURRENT_TIMESTAMP。",
            "将 'DEFAULT NOW()' 改为 'DEFAULT CURRENT_TIMESTAMP'",
            true,
            sql -> sql.replaceAll("(?i)\\bDEFAULT\\s+NOW\\s*\\(\\s*\\)", "DEFAULT CURRENT_TIMESTAMP")
        ));

        // 修复缺少引号的字符串默认值（但排除NULL等特殊关键字和函数调用）
        // 注意：这个模式暂时禁用，因为它会干扰正常的SQL解析过程
        // 根据MySQL 8.4官方文档，DATE_FORMAT(created_at, '%Y-%m')等语法是完全合法的
        // 不应该被错误处理器拦截
        /*
        register(new ErrorPattern(
            "mysql_unquoted_string_default",
            Pattern.compile("(?i)\\bDEFAULT\\s+([a-zA-Z_][a-zA-Z0-9_]*)(?!\\s*\\()(?=\\s*(?:,|\\)|NOT\\s+NULL|NULL|COMMENT))"),
            SqlErrorType.INVALID_DEFAULT_SYNTAX,
            "错误：字符串类型的默认值必须使用引号包围",
            "根据MySQL语法规范，字符串类型的默认值必须使用单引号包围。",
            "将未加引号的字符串默认值加上单引号（排除NULL等特殊关键字和函数调用）",
            true,
            sql -> {
                // 使用更精确的替换逻辑，只在CREATE TABLE上下文中处理DEFAULT子句
                // 排除NULL、TRUE、FALSE等特殊关键字和函数调用
                Pattern pattern = Pattern.compile("(?i)\\bDEFAULT\\s+([a-zA-Z_][a-zA-Z0-9_]*)(?!\\s*\\()(?=\\s*(?:,|\\)|NOT\\s+NULL|NULL|COMMENT))", Pattern.CASE_INSENSITIVE);
                Matcher matcher = pattern.matcher(sql);
                StringBuffer sb = new StringBuffer();

                while (matcher.find()) {
                    String value = matcher.group(1);
                    // 排除特殊关键字，这些不应该加引号
                    if (value.equalsIgnoreCase("NULL") ||
                        value.equalsIgnoreCase("TRUE") ||
                        value.equalsIgnoreCase("FALSE") ||
                        value.equalsIgnoreCase("CURRENT_TIMESTAMP") ||
                        value.equalsIgnoreCase("CURRENT_DATE") ||
                        value.equalsIgnoreCase("CURRENT_TIME")) {
                        matcher.appendReplacement(sb, matcher.group(0)); // 保持原样
                    } else {
                        matcher.appendReplacement(sb, "DEFAULT '" + value + "'");
                    }
                }
                matcher.appendTail(sb);
                return sb.toString();
            }
        ));
        */

        // MySQL LIMIT语法错误
        register(new ErrorPattern(
            "mysql_limit_offset_syntax",
            Pattern.compile("(?i)\\bLIMIT\\s+(\\d+)\\s*,\\s*(\\d+)"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "语法错误：MySQL特有的LIMIT语法",
            "MySQL的 'LIMIT offset, count' 语法在其他数据库中不被支持。标准SQL使用 'LIMIT count OFFSET offset' 语法。",
            "将 'LIMIT offset, count' 改为 'LIMIT count OFFSET offset'",
            true,
            sql -> {
                return sql.replaceAll("(?i)\\bLIMIT\\s+(\\d+)\\s*,\\s*(\\d+)", "LIMIT $2 OFFSET $1");
            }
        ));

        // MySQL ENGINE子句
        register(new ErrorPattern(
            "mysql_engine_clause",
            Pattern.compile("(?i)\\bENGINE\\s*=\\s*\\w+"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性警告：ENGINE子句是MySQL特有的",
            "ENGINE子句在其他数据库中不被支持，转译时会被忽略或转换为注释。",
            "在目标数据库中，存储引擎通过其他方式配置",
            false,
            null
        ));

        // MySQL CHARACTER SET子句
        register(new ErrorPattern(
            "mysql_charset_clause",
            Pattern.compile("(?i)\\b(DEFAULT\\s+)?CHARSET\\s*=\\s*\\w+"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性警告：CHARACTER SET子句是MySQL特有的",
            "CHARACTER SET子句在其他数据库中可能不被支持，需要在数据库级别或连接级别设置字符集。",
            "在目标数据库中通过数据库创建时指定字符集",
            false,
            null
        ));

        // MySQL AUTO_INCREMENT语法
        register(new ErrorPattern(
            "mysql_auto_increment",
            Pattern.compile("(?i)\\bAUTO_INCREMENT\\b"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：AUTO_INCREMENT是MySQL特有语法",
            "AUTO_INCREMENT在其他数据库中需要转换为相应的自增语法，如IDENTITY、SERIAL等。",
            "在目标数据库中使用相应的自增语法",
            false,
            null
        ));

        // MySQL UNSIGNED类型 - 根据各数据库官方文档修正
        // 金仓数据库官方文档表97第6行明确支持UNSIGNED类型，因此不应该移除
        // 达梦和神通数据库可能不支持，但应该由具体的数据库生成器处理，而不是预处理器
        register(new ErrorPattern(
            "mysql_unsigned_type",
            Pattern.compile("(?i)\\b(\\w+)\\s+UNSIGNED\\b"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：UNSIGNED类型的支持因目标数据库而异",
            "UNSIGNED类型在金仓数据库中原生支持，在其他数据库中可能需要转换。",
            "请根据目标数据库的官方文档确认UNSIGNED类型的支持情况",
            false, // 禁用自动修复，让具体的数据库生成器处理
            null
        ));
    }
    
    /**
     * 注册ANTLR通用错误模式
     */
    private static void registerAntlrPatterns() {
        // 无效输入 - 通常是可恢复的警告，不应该阻塞解析
        register(new ErrorPattern(
            "antlr_no_viable_alternative",
            Pattern.compile("(?i)no\\s+viable\\s+alternative\\s+at\\s+input"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "语法兼容性警告：在当前位置发现了歧义的语法结构",
            "ANTLR解析器在当前位置遇到了多个可能的解析路径，但仍能继续解析",
            "这通常是可恢复的警告，如果SQL能正常执行则可以忽略",
            false,
            null
        ));
        
        // 缺少分号
        register(new ErrorPattern(
            "antlr_missing_semicolon",
            Pattern.compile("(?i)missing\\s+';'"),
            SqlErrorType.MISSING_SEMICOLON,
            "语法错误：缺少分号",
            "SQL语句必须以分号(;)结尾",
            "在SQL语句末尾添加分号(;)",
            true,
            sql -> sql.trim().endsWith(";") ? sql : sql.trim() + ";"
        ));


        
        // 意外的输入
        register(new ErrorPattern(
            "antlr_extraneous_input",
            Pattern.compile("(?i)extraneous\\s+input\\s+'([^']+)'"),
            SqlErrorType.UNEXPECTED_TOKEN,
            "语法错误：意外的标记",
            "在当前位置不应该出现此标记",
            "检查SQL语法，移除或修正意外的标记",
            false,
            null
        ));
        
        // 缺少标记
        register(new ErrorPattern(
            "antlr_missing_token",
            Pattern.compile("(?i)missing\\s+'([^']+)'"),
            SqlErrorType.MISSING_TOKEN,
            "语法错误：缺少必需的标记",
            "在当前位置应该有特定的标记",
            "在适当位置添加缺少的标记",
            false,
            null
        ));
        
        // 输入不匹配
        register(new ErrorPattern(
            "antlr_mismatched_input",
            Pattern.compile("(?i)mismatched\\s+input\\s+'([^']+)'\\s+expecting\\s+'([^']+)'"),
            SqlErrorType.MISMATCHED_INPUT,
            "语法错误：输入不匹配",
            "期望特定的标记但得到了其他内容",
            "检查并修正不匹配的标记",
            false,
            null
        ));
    }

    // PostgreSQL语法检测已移除 - 本项目仅支持MySQL语法作为输入
    // 如果用户输入非MySQL语法，应该由MySQL解析器直接报错

    /**
     * 注册SQL通用错误模式
     */
    private static void registerSqlPatterns() {
        // 未闭合的字符串
        register(new ErrorPattern(
            "sql_unclosed_string",
            Pattern.compile("(?i)unterminated\\s+string|unclosed\\s+string|missing\\s+closing\\s+quote"),
            SqlErrorType.UNCLOSED_QUOTE,
            "语法错误：字符串未正确闭合",
            "字符串字面量缺少结束引号",
            "检查字符串是否有匹配的开始和结束引号",
            false,
            null
        ));

        // 无效的列引用
        register(new ErrorPattern(
            "sql_invalid_column",
            Pattern.compile("(?i)unknown\\s+column|column\\s+not\\s+found"),
            SqlErrorType.INVALID_COLUMN_REFERENCE,
            "语法错误：无效的列引用",
            "引用了不存在的列或列名拼写错误",
            "检查列名是否正确，确保列存在于相应的表中",
            false,
            null
        ));

        // 无效的表引用
        register(new ErrorPattern(
            "sql_invalid_table",
            Pattern.compile("(?i)table\\s+.*\\s+doesn't\\s+exist|unknown\\s+table"),
            SqlErrorType.INVALID_TABLE_REFERENCE,
            "语法错误：无效的表引用",
            "引用了不存在的表或表名拼写错误",
            "检查表名是否正确，确保表存在于数据库中",
            false,
            null
        ));

        // VARCHAR长度检测已移至专门的验证阶段，不在解析阶段进行
        // 这样可以避免阻塞SQL解析过程，同时保持数据验证的完整性

        // MySQL特有的文本数据类型（需要转换但不拒绝）
        // 注意：TINYINT和MEDIUMINT是MySQL 8.4官方标准整数类型，不应被标记为不支持
        // 参考：https://dev.mysql.com/doc/refman/8.4/en/integer-types.html
        register(new ErrorPattern(
            "sql_mysql_specific_text_types",
            Pattern.compile("(?i)\\b(TINYTEXT|MEDIUMTEXT|LONGTEXT|ENUM|SET)\\b"),
            SqlErrorType.DATA_TYPE_COMPATIBILITY,
            "数据类型兼容性警告：使用了MySQL特有的文本数据类型",
            "某些文本数据类型在其他数据库中不被支持，需要转换为兼容的类型。",
            "使用标准SQL数据类型或目标数据库支持的等价类型",
            false,
            null
        ));

        // 不支持的函数
        register(new ErrorPattern(
            "sql_unsupported_function",
            Pattern.compile("(?i)\\b(GROUP_CONCAT|IFNULL|DATE_FORMAT|STR_TO_DATE|UNIX_TIMESTAMP)\\s*\\("),
            SqlErrorType.FUNCTION_COMPATIBILITY,
            "函数兼容性警告：使用了MySQL特有的函数",
            "某些函数在其他数据库中不被支持，需要转换为等价的函数或表达式。",
            "使用目标数据库支持的等价函数",
            false,
            null
        ));

        // 反引号标识符 - 不在解析阶段处理，由生成器在生成阶段处理
        // 这样可以保证ANTLR解析器能够正确解析包含特殊字符的标识符
        // register(new ErrorPattern(
        //     "sql_backtick_identifier",
        //     Pattern.compile("`[^`]+`"),
        //     SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
        //     "语法兼容性提示：使用了MySQL特有的反引号标识符",
        //     "反引号标识符是MySQL特有的语法，由目标数据库生成器在生成阶段处理。",
        //     "在目标数据库中使用相应的标识符引用符号",
        //     false,
        //     null
        // ));

        // 注释语法
        register(new ErrorPattern(
            "sql_mysql_comment",
            Pattern.compile("(?i)\\bCOMMENT\\s+['\"][^'\"]*['\"]"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "语法兼容性提示：MySQL的COMMENT语法",
            "MySQL的COMMENT语法在其他数据库中可能需要转换为COMMENT ON语句。",
            "在目标数据库中使用相应的注释语法",
            false,
            null
        ));

        // 注册通用的MySQL语法规范检测模式
        registerMySqlSyntaxValidationPatterns();

        // PostgreSQL数组语法（排除MySQL JSON路径表达式）
        // 根据 .augment/rules/rule-db.md 要求，不应误报正确的MySQL语法
        register(new ErrorPattern(
            "postgresql_array_syntax",
            Pattern.compile("(?<!\\$\\.)(?<!\\')\\b\\w+\\[\\d+\\](?!\\w)|\\bARRAY\\[.*\\]"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "语法错误：使用了PostgreSQL特有的数组语法",
            "PostgreSQL的数组语法不符合MySQL规范。MySQL不支持原生数组类型。",
            "请使用MySQL支持的数据类型，如JSON类型来存储数组数据",
            false,
            null
        ));

        // PostgreSQL SERIAL类型
        register(new ErrorPattern(
            "postgresql_serial_type",
            Pattern.compile("(?i)\\b(SERIAL|BIGSERIAL|SMALLSERIAL)\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "语法错误：使用了PostgreSQL特有的SERIAL类型",
            "PostgreSQL的SERIAL类型不符合MySQL规范。",
            "请使用MySQL的AUTO_INCREMENT属性：INT AUTO_INCREMENT",
            false,
            null
        ));
    }

    /**
     * 注册数据库转译特定错误模式
     */
    private static void registerTranspilationPatterns() {
        // SET语句兼容性问题
        register(new ErrorPattern(
            "transpilation_set_statement",
            Pattern.compile("(?i)\\bSET\\s+(FOREIGN_KEY_CHECKS|SQL_MODE|CHARACTER_SET_CLIENT|NAMES|TIME_ZONE)\\b"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性警告：SET语句可能不被目标数据库支持",
            "某些SET语句是MySQL特有的，在其他数据库中可能不被支持或需要不同的语法。",
            "检查目标数据库的相应配置方法",
            false,
            null
        ));

        // USE语句兼容性问题
        register(new ErrorPattern(
            "transpilation_use_statement",
            Pattern.compile("(?i)\\bUSE\\s+\\w+"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性警告：USE语句在某些数据库中不被支持",
            "USE语句在某些数据库（如KingbaseES）中不被支持，需要在连接字符串中指定数据库。",
            "在连接字符串中指定数据库名称",
            false,
            null
        ));

        // LOCK/UNLOCK TABLES语句
        register(new ErrorPattern(
            "transpilation_lock_tables",
            Pattern.compile("(?i)\\b(LOCK|UNLOCK)\\s+TABLES?\\b"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性警告：LOCK/UNLOCK TABLES是MySQL特有语法",
            "LOCK/UNLOCK TABLES语句在其他数据库中可能不被支持或有不同的语法。",
            "使用目标数据库的事务控制或锁定机制",
            false,
            null
        ));

        // 存储过程语法差异
        register(new ErrorPattern(
            "transpilation_procedure_syntax",
            Pattern.compile("(?i)\\bDELIMITER\\s+"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性警告：DELIMITER是MySQL特有的存储过程语法",
            "DELIMITER语句用于MySQL存储过程，其他数据库有不同的存储过程语法。",
            "使用目标数据库的存储过程语法",
            false,
            null
        ));

        // 窗口函数兼容性
        register(new ErrorPattern(
            "transpilation_window_function",
            Pattern.compile("(?i)\\b(ROW_NUMBER|RANK|DENSE_RANK|LEAD|LAG)\\s*\\(.*\\)\\s+OVER\\s*\\("),
            SqlErrorType.FUNCTION_COMPATIBILITY,
            "兼容性提示：窗口函数在旧版本数据库中可能不被支持",
            "窗口函数是SQL:2003标准，但在某些旧版本数据库中可能不被支持。",
            "确认目标数据库版本支持窗口函数",
            false,
            null
        ));

        // CTE（公共表表达式）兼容性
        register(new ErrorPattern(
            "transpilation_cte_syntax",
            Pattern.compile("(?i)\\bWITH\\s+\\w+\\s+AS\\s*\\("),
            SqlErrorType.FUNCTION_COMPATIBILITY,
            "兼容性提示：CTE（公共表表达式）在某些数据库中可能不被支持",
            "WITH子句（CTE）是SQL:1999标准，但在某些数据库中可能不被支持。",
            "确认目标数据库支持CTE或使用子查询替代",
            false,
            null
        ));
    }

    /**
     * 注册MySQL 8.4特定错误模式
     * 基于MySQL 8.4官方文档中已移除和弃用的功能
     */
    private static void registerMySql84Patterns() {
        // MySQL 8.4中已移除的复制语法
        register(new ErrorPattern(
            "mysql84_removed_replication_syntax",
            Pattern.compile("(?i)\\b(CHANGE\\s+MASTER\\s+TO|RESET\\s+MASTER|SHOW\\s+MASTER\\s+STATUS|PURGE\\s+MASTER\\s+LOGS|SHOW\\s+MASTER\\s+LOGS)\\b"),
            SqlErrorType.MYSQL_84_REMOVED_FEATURE,
            "MySQL 8.4错误：使用了已移除的复制语法",
            "MySQL 8.4中已移除旧的复制语法，必须使用新的SOURCE语法。例如：CHANGE MASTER TO → CHANGE REPLICATION SOURCE TO",
            "使用新的SOURCE语法替代MASTER语法",
            false,
            null
        ));

        register(new ErrorPattern(
            "mysql84_removed_slave_syntax",
            Pattern.compile("(?i)\\b(START\\s+SLAVE|STOP\\s+SLAVE|SHOW\\s+SLAVE\\s+STATUS|SHOW\\s+SLAVE\\s+HOSTS|RESET\\s+SLAVE)\\b"),
            SqlErrorType.MYSQL_84_REMOVED_FEATURE,
            "MySQL 8.4错误：使用了已移除的SLAVE语法",
            "MySQL 8.4中已移除SLAVE相关语法，必须使用REPLICA语法。例如：START SLAVE → START REPLICA",
            "使用REPLICA语法替代SLAVE语法",
            false,
            null
        ));

        // MySQL 8.4中已移除的服务器选项
        register(new ErrorPattern(
            "mysql84_removed_server_options",
            Pattern.compile("(?i)\\b(expire_logs_days|default_authentication_plugin|binlog_transaction_dependency_tracking)\\b"),
            SqlErrorType.MYSQL_84_REMOVED_FEATURE,
            "MySQL 8.4错误：使用了已移除的服务器变量",
            "这些服务器变量在MySQL 8.4中已被移除。例如：expire_logs_days → binlog_expire_logs_seconds",
            "使用新的替代变量",
            false,
            null
        ));

        // MySQL 8.4中已移除的函数
        register(new ErrorPattern(
            "mysql84_removed_functions",
            Pattern.compile("(?i)\\bWAIT_UNTIL_SQL_THREAD_AFTER_GTIDS\\s*\\("),
            SqlErrorType.MYSQL_84_REMOVED_FEATURE,
            "MySQL 8.4错误：使用了已移除的函数",
            "WAIT_UNTIL_SQL_THREAD_AFTER_GTIDS()函数在MySQL 8.4中已被移除",
            "使用替代的GTID等待函数",
            false,
            null
        ));

        // MySQL 8.4中弃用的功能
        register(new ErrorPattern(
            "mysql84_deprecated_charset_handshake",
            Pattern.compile("(?i)--character-set-client-handshake"),
            SqlErrorType.MYSQL_84_DEPRECATED_FEATURE,
            "MySQL 8.4弃用警告：character-set-client-handshake选项已弃用",
            "此选项在MySQL 8.4中已弃用，将在未来版本中移除",
            "迁移到新的字符集处理方式",
            false,
            null
        ));

        // 非标准外键警告
        register(new ErrorPattern(
            "mysql84_nonstandard_foreign_keys",
            Pattern.compile("(?i)\\bFOREIGN\\s+KEY\\b.*\\bREFERENCES\\b"),
            SqlErrorType.MYSQL_84_DEPRECATED_FEATURE,
            "MySQL 8.4兼容性警告：非标准外键的使用",
            "MySQL 8.4默认启用restrict_fk_on_non_standard_key，非标准外键可能被拒绝",
            "确保外键引用唯一键或主键",
            false,
            null
        ));

        // AUTO_INCREMENT与浮点数
        register(new ErrorPattern(
            "mysql84_auto_increment_float",
            Pattern.compile("(?i)\\b(FLOAT|DOUBLE)\\b.*\\bAUTO_INCREMENT\\b"),
            SqlErrorType.MYSQL_84_REMOVED_FEATURE,
            "MySQL 8.4错误：FLOAT/DOUBLE列不能使用AUTO_INCREMENT",
            "MySQL 8.4中完全移除了对FLOAT和DOUBLE列使用AUTO_INCREMENT的支持",
            "使用整数类型的AUTO_INCREMENT列",
            false,
            null
        ));

        // 弃用的数据库通配符
        register(new ErrorPattern(
            "mysql84_database_wildcards",
            Pattern.compile("(?i)\\bGRANT\\b.*\\bON\\s+[`'\"]?[^`'\"]*[%_][^`'\"]*[`'\"]?\\."),
            SqlErrorType.MYSQL_84_DEPRECATED_FEATURE,
            "MySQL 8.4弃用警告：数据库授权中的通配符已弃用",
            "在数据库授权中使用%和_作为通配符已在MySQL 8.2.0中弃用",
            "避免在数据库名中使用通配符",
            false,
            null
        ));
    }

    /**
     * 注册高级SQL特性错误模式
     * 基于MySQL官方文档和常见的兼容性问题
     */
    private static void registerAdvancedSqlPatterns() {
        // JSON函数兼容性
        register(new ErrorPattern(
            "advanced_json_functions",
            Pattern.compile("(?i)\\b(JSON_EXTRACT|JSON_UNQUOTE|JSON_SET|JSON_INSERT|JSON_REPLACE|JSON_REMOVE|JSON_ARRAY|JSON_OBJECT)\\s*\\("),
            SqlErrorType.FUNCTION_COMPATIBILITY,
            "兼容性提示：JSON函数在旧版本数据库中可能不被支持",
            "JSON函数是MySQL 5.7+的特性，在更早版本或其他数据库中可能不被支持",
            "确认目标数据库支持JSON函数或使用替代方案",
            false,
            null
        ));

        // 生成列（Generated Columns）
        register(new ErrorPattern(
            "advanced_generated_columns",
            Pattern.compile("(?i)\\bGENERATED\\s+ALWAYS\\s+AS\\s*\\("),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：生成列是MySQL特有功能",
            "生成列（Generated Columns）是MySQL 5.7+的特性，其他数据库可能不支持",
            "在目标数据库中使用触发器或应用层逻辑实现",
            false,
            null
        ));

        // 分区表语法
        register(new ErrorPattern(
            "advanced_partitioning",
            Pattern.compile("(?i)\\bPARTITION\\s+BY\\s+(RANGE|LIST|HASH|KEY)\\s*\\("),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：分区表语法可能不被所有数据库支持",
            "MySQL的分区语法在其他数据库中可能有不同的实现",
            "检查目标数据库的分区支持和语法",
            false,
            null
        ));

        // 全文索引
        register(new ErrorPattern(
            "advanced_fulltext_index",
            Pattern.compile("(?i)\\bFULLTEXT\\s+(INDEX|KEY)\\b"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：FULLTEXT索引语法可能不兼容",
            "FULLTEXT索引的语法在不同数据库中可能有差异",
            "检查目标数据库的全文搜索实现",
            false,
            null
        ));

        // 空间数据类型
        register(new ErrorPattern(
            "advanced_spatial_types",
            Pattern.compile("(?i)\\b(GEOMETRY|POINT|LINESTRING|POLYGON|MULTIPOINT|MULTILINESTRING|MULTIPOLYGON|GEOMETRYCOLLECTION)\\b"),
            SqlErrorType.DATA_TYPE_COMPATIBILITY,
            "兼容性提示：空间数据类型可能不被支持",
            "空间数据类型在不同数据库中的支持程度不同",
            "确认目标数据库支持相应的空间数据类型",
            false,
            null
        ));

        // 递归CTE深度限制
        register(new ErrorPattern(
            "advanced_recursive_cte",
            Pattern.compile("(?i)\\bWITH\\s+RECURSIVE\\b"),
            SqlErrorType.FUNCTION_COMPATIBILITY,
            "兼容性提示：递归CTE可能有深度限制",
            "递归CTE在不同数据库中可能有不同的深度限制和性能特征",
            "注意递归深度限制，考虑性能影响",
            false,
            null
        ));

        // 表达式索引
        register(new ErrorPattern(
            "advanced_expression_index",
            Pattern.compile("(?i)\\bINDEX\\s+\\w+\\s*\\(\\s*\\([^)]+\\)\\s*\\)"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：表达式索引语法可能不兼容",
            "基于表达式的索引在不同数据库中语法可能不同",
            "检查目标数据库的表达式索引语法",
            false,
            null
        ));

        // 检查约束（MySQL 8.0.16+）
        register(new ErrorPattern(
            "advanced_check_constraints",
            Pattern.compile("(?i)\\bCHECK\\s*\\([^)]+\\)"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：CHECK约束在MySQL 8.0.16+才支持",
            "CHECK约束在MySQL早期版本中不被支持，在其他数据库中语法可能不同",
            "确认目标数据库版本支持CHECK约束",
            false,
            null
        ));

        // 不可见列（Invisible Columns）
        register(new ErrorPattern(
            "advanced_invisible_columns",
            Pattern.compile("(?i)\\bINVISIBLE\\b"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：不可见列是MySQL 8.0.23+的特性",
            "不可见列（INVISIBLE）是MySQL的特有功能，其他数据库可能不支持",
            "在目标数据库中移除INVISIBLE关键字或使用其他方案",
            false,
            null
        ));

        // 多值索引（Multi-Valued Indexes）
        register(new ErrorPattern(
            "advanced_multivalue_index",
            Pattern.compile("(?i)\\bINDEX\\s+\\w+\\s*\\(\\s*\\(\\s*CAST\\s*\\([^)]+\\s+AS\\s+.*ARRAY\\s*\\)\\s*\\)\\s*\\)"),
            SqlErrorType.MYSQL_SPECIFIC_SYNTAX,
            "兼容性提示：多值索引是MySQL 8.0.17+的特性",
            "多值索引（Multi-Valued Indexes）是MySQL的特有功能",
            "在目标数据库中使用传统索引或其他方案",
            false,
            null
        ));
    }

    /**
     * 注册神通数据库特定错误模式
     * 基于神通数据库官方文档的限制和不支持的功能
     */
    private static void registerShentongPatterns() {
        // 神通数据库不支持的MySQL自增步长和偏移量
        register(new ErrorPattern(
            "shentong_auto_increment_step",
            Pattern.compile("(?i)\\bSET\\s+@@(AUTO_INCREMENT_INCREMENT|AUTO_INCREMENT_OFFSET)\\s*=\\s*\\d+"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库不支持：MySQL的自增列步长和偏移量设置",
            "根据神通数据库官方文档，暂不支持MySQL的修改自增列的自增步长和偏移量功能。",
            "移除AUTO_INCREMENT_INCREMENT和AUTO_INCREMENT_OFFSET设置",
            false,
            null
        ));

        // 神通数据库不支持的XML namespace功能
        register(new ErrorPattern(
            "shentong_xml_namespace",
            Pattern.compile("(?i)\\b(EXTRACTVALUE|EXISTSNODE|DELETEXML|APPENDCHILDXML)\\s*\\([^)]*namespace[^)]*\\)"),
            SqlErrorType.FUNCTION_COMPATIBILITY,
            "神通数据库不支持：XML函数的namespace参数",
            "根据神通数据库官方文档，XML函数暂不支持指定namespace。",
            "移除XML函数中的namespace参数",
            false,
            null
        ));

        // 神通数据库不支持的临时表分区功能
        register(new ErrorPattern(
            "shentong_temp_table_partition",
            Pattern.compile("(?i)\\bCREATE\\s+(TEMPORARY|TEMP)\\s+TABLE\\s+[^;]*\\bPARTITION\\s+BY\\s+(RANGE|HASH|LIST)\\s*\\([^)]*\\)"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库不支持：临时表的分区功能",
            "根据神通数据库官方文档，临时表不支持分区。",
            "移除临时表中的分区定义",
            false,
            null
        ));

        // 神通数据库不支持的临时表外键约束
        register(new ErrorPattern(
            "shentong_temp_table_foreign_key",
            Pattern.compile("(?i)\\bCREATE\\s+(TEMPORARY|TEMP)\\s+TABLE\\s+\\w+\\s*\\([^)]*,\\s*FOREIGN\\s+KEY\\s*\\([^)]+\\)\\s+REFERENCES\\s+\\w+\\s*\\([^)]+\\)[^)]*\\)"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库不支持：临时表的外键约束",
            "根据神通数据库官方文档，临时表不支持外键约束。",
            "移除临时表中的外键约束定义",
            false,
            null
        ));

        // 神通数据库不支持的LOB缓存功能
        register(new ErrorPattern(
            "shentong_lob_cache",
            Pattern.compile("(?i)\\b(BLOB|CLOB)\\s+(CACHE|CACHE\\s+READS|NOCACHE)\\b"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库暂不支持：LOB缓存功能",
            "根据神通数据库官方文档，暂不支持缓存LOB页功能。",
            "移除LOB缓存相关设置",
            false,
            null
        ));

        // 神通数据库不支持的分区操作
        register(new ErrorPattern(
            "shentong_partition_limitations",
            Pattern.compile("(?i)\\b(ALTER\\s+TABLE\\s+\\w+\\s+)?(MERGE|SPLIT)\\s+PARTITION\\s+\\w+\\s+(HASH|LIST)"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库不支持：HASH和LIST分区的合并/分裂操作",
            "根据神通数据库官方文档，分区表的合并和分裂操作不支持Hash分区。",
            "使用Range分区或重新设计分区策略",
            false,
            null
        ));

        // 神通数据库不支持的索引类型
        register(new ErrorPattern(
            "shentong_index_limitations",
            Pattern.compile("(?i)\\bCREATE\\s+(FULLTEXT\\s+)?INDEX\\s+\\w+\\s+ON\\s+\\w+\\s*\\([^)]*\\)\\s+(USING\\s+(HASH|RTREE)|ALGORITHM\\s*=\\s*(HASH|RTREE))"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库不支持：HASH和RTREE索引算法",
            "根据神通数据库官方文档，不支持HASH和RTREE索引算法。",
            "使用默认的B+树索引算法",
            false,
            null
        ));

        // 神通数据库不支持的存储过程返回结果集
        register(new ErrorPattern(
            "shentong_procedure_resultset",
            Pattern.compile("(?i)\\bPREPARE\\s+\\w+\\s+FROM\\s+['\"]CALL\\s+\\w+\\s*\\([^)]*\\)['\"]"),
            SqlErrorType.FUNCTION_COMPATIBILITY,
            "神通数据库不支持：PREPARE语句执行返回结果集的存储过程",
            "根据神通数据库官方文档，PREPARE时使用返回结果集的存储过程没有意义，因为EXECUTE不支持。",
            "直接调用存储过程，不使用PREPARE语句",
            false,
            null
        ));

        // 神通数据库不支持的游标功能
        register(new ErrorPattern(
            "shentong_cursor_limitations",
            Pattern.compile("(?i)\\b(FETCH\\s+ABSOLUTE|MOVE\\s+ABSOLUTE)\\s+\\d+"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库暂不支持：游标的ABSOLUTE定位功能",
            "根据神通数据库官方文档，ABSOLUTE暂不支持。",
            "使用RELATIVE定位或重新设计游标逻辑",
            false,
            null
        ));

        // 神通数据库不支持的数据类型
        register(new ErrorPattern(
            "shentong_unsupported_types",
            Pattern.compile("(?i)\\b(GEOMETRY|POINT|LINESTRING|POLYGON|MULTIPOINT|MULTILINESTRING|MULTIPOLYGON|GEOMETRYCOLLECTION)\\b"),
            SqlErrorType.DATA_TYPE_COMPATIBILITY,
            "神通数据库不支持：空间数据类型",
            "根据神通数据库官方文档，不支持MySQL的空间数据类型。",
            "使用VARCHAR或BLOB类型存储空间数据，或使用专门的空间数据库",
            false,
            null
        ));

        // 神通数据库不支持的SELECT FOR UPDATE变体
        register(new ErrorPattern(
            "shentong_select_for_update",
            Pattern.compile("(?i)\\bSELECT\\s+.*\\s+FOR\\s+UPDATE\\s+(OF\\s+\\w+\\s*)?(SKIP\\s+LOCKED|NOWAIT)"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库不支持：SELECT FOR UPDATE的SKIP LOCKED和NOWAIT选项",
            "根据神通数据库官方文档，SELECT FOR UPDATE的高级选项不被支持。",
            "使用基本的SELECT FOR UPDATE语法",
            false,
            null
        ));

        // 神通数据库层次查询环检测限制
        register(new ErrorPattern(
            "shentong_hierarchical_cycle",
            Pattern.compile("(?i)\\bCONNECT\\s+BY\\s+(?!.*PRIOR)"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库注意：层次查询中没有PRIOR关键字时不会进行环检测",
            "根据神通数据库官方文档，如果在CONNECT BY后的条件中没有PRIOR关键字，神通数据库由于有最大层次限制，会提示超过最大层次数。",
            "在CONNECT BY条件中添加PRIOR关键字以启用环检测",
            false,
            null
        ));

        // 神通数据库不支持的BULK COLLECT限制
        register(new ErrorPattern(
            "shentong_bulk_collect_string_index",
            Pattern.compile("(?i)\\bBULK\\s+COLLECT\\s+INTO\\s+\\w+\\s*\\(\\s*VARCHAR2?\\s*\\)"),
            SqlErrorType.SYNTAX_COMPATIBILITY,
            "神通数据库不支持：对字符串类型键值的索引表使用BULK COLLECT",
            "根据神通数据库官方文档，不能对使用字符串类型做键值类型的索引表使用BULK COLLECT子句。",
            "使用数字类型作为索引表的键值类型",
            false,
            null
        ));
    }

    /**
     * 注册错误模式
     */
    public static void register(ErrorPattern pattern) {
        ERROR_PATTERNS.add(pattern);
        log.debug("Registered error pattern: {}", pattern.getId());
    }
    
    /**
     * 查找匹配的错误模式
     */
    public static Optional<ErrorPattern> findMatchingPattern(String sql, String errorMessage) {
        for (ErrorPattern pattern : ERROR_PATTERNS) {
            if (pattern.matches(sql, errorMessage)) {
                log.debug("Found matching pattern: {} for error: {}", pattern.getId(), errorMessage);
                return Optional.of(pattern);
            }
        }
        return Optional.empty();
    }
    
    /**
     * 获取所有可自动修复的模式
     */
    public static List<ErrorPattern> getAutoFixablePatterns() {
        return ERROR_PATTERNS.stream()
            .filter(ErrorPattern::isAutoFixable)
            .toList();
    }
    
    /**
     * 检查SQL是否包含已知的语法问题
     * 只有当自动修复会实际进行修改时才返回true
     */
    public static boolean hasKnownSyntaxIssues(String sql) {
        for (ErrorPattern pattern : getAutoFixablePatterns()) {
            if (pattern.matchesSql(sql) && pattern.getAutoFixer() != null) {
                String fixedSql = pattern.getAutoFixer().apply(sql);
                if (!fixedSql.equals(sql)) {
                    return true; // 只有当修复会产生变化时才认为有问题
                }
            }
        }
        return false;
    }
    
    /**
     * 自动修复已知的语法问题
     */
    public static String autoFixKnownIssues(String sql) {
        String fixed = sql;
        for (ErrorPattern pattern : getAutoFixablePatterns()) {
            if (pattern.matchesSql(fixed) && pattern.getAutoFixer() != null) {
                String newFixed = pattern.getAutoFixer().apply(fixed);
                if (!newFixed.equals(fixed)) {
                    log.info("Applied auto-fix for pattern: {}", pattern.getId());
                    fixed = newFixed;
                }
            }
        }
        return fixed;
    }

    /**
     * 获取所有错误模式的数量
     */
    public static int getPatternCount() {
        return ERROR_PATTERNS.size();
    }


    
    /**
     * 错误模式定义
     */
    @Data
    public static class ErrorPattern {
        private final String id;
        private final Pattern sqlPattern;
        private final Pattern errorPattern;
        private final SqlErrorType errorType;
        private final String friendlyMessage;
        private final String detailedMessage;
        private final String suggestion;
        private final boolean autoFixable;
        private final java.util.function.Function<String, String> autoFixer;
        
        public ErrorPattern(String id, Pattern pattern, SqlErrorType errorType, 
                          String friendlyMessage, String detailedMessage, String suggestion,
                          boolean autoFixable, java.util.function.Function<String, String> autoFixer) {
            this.id = id;
            this.sqlPattern = pattern;
            this.errorPattern = null; // 用于SQL模式匹配
            this.errorType = errorType;
            this.friendlyMessage = friendlyMessage;
            this.detailedMessage = detailedMessage;
            this.suggestion = suggestion;
            this.autoFixable = autoFixable;
            this.autoFixer = autoFixer;
        }
        
        public ErrorPattern(String id, Pattern errorPattern, SqlErrorType errorType, 
                          String friendlyMessage, String detailedMessage, String suggestion,
                          boolean autoFixable) {
            this.id = id;
            this.sqlPattern = null;
            this.errorPattern = errorPattern; // 用于错误消息模式匹配
            this.errorType = errorType;
            this.friendlyMessage = friendlyMessage;
            this.detailedMessage = detailedMessage;
            this.suggestion = suggestion;
            this.autoFixable = autoFixable;
            this.autoFixer = null;
        }
        
        public boolean matches(String sql, String errorMessage) {
            if (sqlPattern != null) {
                return sqlPattern.matcher(sql).find();
            }
            if (errorPattern != null) {
                return errorPattern.matcher(errorMessage).find();
            }
            return false;
        }
        
        public boolean matchesSql(String sql) {
            return sqlPattern != null && sqlPattern.matcher(sql).find();
        }
        
        public boolean matchesError(String errorMessage) {
            return errorPattern != null && errorPattern.matcher(errorMessage).find();
        }
    }

    /**
     * 注册MySQL语法规范检测模式
     * 基于MySQL官方文档实现通用的语法验证
     */
    private static void registerMySqlSyntaxValidationPatterns() {
        // PostgreSQL类型转换语法检测 - MySQL不支持 :: 语法
        register(new ErrorPattern(
            "non_mysql_type_cast",
            Pattern.compile("\\w+::(text|varchar|timestamp|int|integer|bigint|decimal|numeric|boolean|date|time|char)\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 '::' 类型转换语法",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL风格的 '::' 类型转换语法。MySQL使用CAST()函数或CONVERT()函数进行类型转换。",
            "请使用MySQL标准语法：CAST(expression AS type) 或 CONVERT(expression, type)",
            false,
            null
        ));

        // 非MySQL标准函数检测 - 基于MySQL官方函数列表
        registerNonMySqlFunctions();

        // 移除重复的PostgreSQL数组语法检测（已在上面定义）

        // PostgreSQL SERIAL类型检测
        register(new ErrorPattern(
            "postgresql_serial_type",
            Pattern.compile("(?i)\\b(SERIAL|BIGSERIAL|SMALLSERIAL)\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持SERIAL类型",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL的SERIAL类型。",
            "请使用MySQL的AUTO_INCREMENT属性：INT AUTO_INCREMENT",
            false,
            null
        ));

        // PostgreSQL特有的操作符
        register(new ErrorPattern(
            "postgresql_operators",
            Pattern.compile("(?i)\\b(ILIKE|SIMILAR\\s+TO|~|~\\*|!~|!~\\*)\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：使用了非MySQL标准操作符",
            "根据MySQL 8.4官方文档，检测到的操作符不符合MySQL规范。",
            "请使用MySQL标准操作符：LIKE, REGEXP, NOT REGEXP等",
            false,
            null
        ));
    }

    /**
     * 注册非MySQL标准函数检测
     * 严格基于MySQL 8.4官方文档的函数列表进行验证
     * 参考：https://dev.mysql.com/doc/refman/8.4/en/mathematical-functions.html
     */
    private static void registerNonMySqlFunctions() {
        // random()函数检测 - 根据MySQL 8.4官方文档，MySQL使用RAND()
        register(new ErrorPattern(
            "non_mysql_random_function",
            Pattern.compile("(?i)\\brandom\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 random() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持 random() 函数。这是PostgreSQL特有的函数。MySQL使用 RAND() 函数生成随机数。",
            "请使用MySQL标准函数：RAND()",
            false,
            null
        ));

        // clock_timestamp()函数检测 - MySQL使用NOW()或CURRENT_TIMESTAMP
        register(new ErrorPattern(
            "non_mysql_clock_timestamp",
            Pattern.compile("(?i)\\bclock_timestamp\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 clock_timestamp() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持 clock_timestamp() 函数。MySQL使用 NOW() 或 CURRENT_TIMESTAMP 获取当前时间戳。",
            "请使用MySQL标准函数：NOW() 或 CURRENT_TIMESTAMP",
            false,
            null
        ));

        // generate_series()函数检测 - PostgreSQL特有函数
        register(new ErrorPattern(
            "non_mysql_generate_series",
            Pattern.compile("(?i)\\bgenerate_series\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 generate_series() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持 generate_series() 函数。这是PostgreSQL特有的表值函数。",
            "请使用MySQL的递归CTE或其他方式生成数字序列",
            false,
            null
        ));

        // array_agg()函数检测 - PostgreSQL特有聚合函数
        register(new ErrorPattern(
            "non_mysql_array_agg",
            Pattern.compile("(?i)\\barray_agg\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 array_agg() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持 array_agg() 聚合函数。这是PostgreSQL特有的聚合函数。",
            "请使用MySQL的GROUP_CONCAT()函数或JSON_ARRAYAGG()函数",
            false,
            null
        ));

        // ILIKE操作符检测 - PostgreSQL特有操作符
        register(new ErrorPattern(
            "non_mysql_ilike",
            Pattern.compile("(?i)\\bILIKE\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 ILIKE 操作符",
            "根据MySQL 8.4官方文档，MySQL不支持 ILIKE 操作符。这是PostgreSQL特有的大小写不敏感LIKE操作符。",
            "请使用MySQL的LIKE操作符配合LOWER()函数或设置适当的排序规则",
            false,
            null
        ));

        // ::text类型转换语法检测 - PostgreSQL特有语法
        register(new ErrorPattern(
            "non_mysql_cast_syntax",
            Pattern.compile("(?i)::(text|varchar|char|int|integer|bigint|decimal|numeric|float|double|boolean|bool|date|time|timestamp)\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 ::type 类型转换语法",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL的 ::type 类型转换语法。MySQL使用 CAST() 或 CONVERT() 函数进行类型转换。",
            "请使用MySQL标准语法：CAST(expression AS type) 或 CONVERT(expression, type)",
            false,
            null
        ));

        // ||字符串拼接操作符检测 - PostgreSQL语法（在MySQL默认配置下不支持）
        register(new ErrorPattern(
            "non_mysql_concat_operator",
            Pattern.compile("(?i)\\|\\|(?!\\s*\\|)"), // 匹配||但不匹配|||（避免误报位运算）
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：|| 字符串拼接操作符在默认配置下不支持",
            "根据MySQL 8.4官方文档，|| 操作符默认用于逻辑OR运算。虽然可以通过设置 sql_mode='PIPES_AS_CONCAT' 启用字符串拼接，但不建议使用。",
            "请使用MySQL标准函数：CONCAT(str1, str2, ...) 进行字符串拼接",
            false,
            null
        ));

        // RETURNING子句检测 - PostgreSQL特有语法
        register(new ErrorPattern(
            "non_mysql_returning",
            Pattern.compile("(?i)\\bRETURNING\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 RETURNING 子句",
            "根据MySQL 8.4官方文档，MySQL不支持 RETURNING 子句。这是PostgreSQL特有的语法。",
            "请使用MySQL的LAST_INSERT_ID()函数或单独的SELECT语句获取插入的数据",
            false,
            null
        ));

        // string_agg()函数检测 - PostgreSQL特有聚合函数
        register(new ErrorPattern(
            "non_mysql_string_agg",
            Pattern.compile("(?i)\\bstring_agg\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 string_agg() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持 string_agg() 聚合函数。这是PostgreSQL特有的字符串聚合函数。",
            "请使用MySQL的GROUP_CONCAT()函数",
            false,
            null
        ));

        // unnest()函数检测 - PostgreSQL特有数组函数
        register(new ErrorPattern(
            "non_mysql_unnest",
            Pattern.compile("(?i)\\bunnest\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 unnest() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持 unnest() 函数。这是PostgreSQL特有的数组展开函数。",
            "请使用MySQL的JSON_TABLE()函数或其他方式处理数组数据",
            false,
            null
        ));

        // array_length()函数检测 - PostgreSQL特有数组函数
        register(new ErrorPattern(
            "non_mysql_array_length",
            Pattern.compile("(?i)\\barray_length\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 array_length() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持 array_length() 函数。这是PostgreSQL特有的数组长度函数。",
            "请使用MySQL的JSON_LENGTH()函数处理JSON数组",
            false,
            null
        ));

        // PostgreSQL字符串连接操作符检测
        register(new ErrorPattern(
            "non_mysql_string_concat_operator",
            Pattern.compile("\\|\\|"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 || 字符串连接操作符",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL风格的 || 字符串连接操作符。",
            "请使用MySQL的CONCAT()函数进行字符串连接",
            false,
            null
        ));

        // PostgreSQL正则表达式操作符检测
        register(new ErrorPattern(
            "non_mysql_regex_operators",
            Pattern.compile("(?i)\\s+[!]?~\\s+"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 ~ 或 !~ 正则表达式操作符",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL风格的 ~ 和 !~ 正则表达式操作符。",
            "请使用MySQL的REGEXP或RLIKE操作符",
            false,
            null
        ));

        // SQL Server GETDATE()函数检测
        register(new ErrorPattern(
            "non_mysql_getdate",
            Pattern.compile("(?i)\\bGETDATE\\s*\\("),
            SqlErrorType.SQLSERVER_SYNTAX_ERROR,
            "MySQL语法错误：不支持 GETDATE() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持SQL Server的GETDATE()函数。",
            "请使用MySQL的NOW()或CURRENT_TIMESTAMP函数",
            false,
            null
        ));

        // SQL Server LEN()函数检测
        register(new ErrorPattern(
            "non_mysql_len",
            Pattern.compile("(?i)\\bLEN\\s*\\("),
            SqlErrorType.SQLSERVER_SYNTAX_ERROR,
            "MySQL语法错误：不支持 LEN() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持SQL Server的LEN()函数。",
            "请使用MySQL的LENGTH()或CHAR_LENGTH()函数",
            false,
            null
        ));

        // SQL Server DATEPART()函数检测
        register(new ErrorPattern(
            "non_mysql_datepart",
            Pattern.compile("(?i)\\bDATEPART\\s*\\("),
            SqlErrorType.SQLSERVER_SYNTAX_ERROR,
            "MySQL语法错误：不支持 DATEPART() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持SQL Server的DATEPART()函数。",
            "请使用MySQL的EXTRACT()、YEAR()、MONTH()、DAY()等函数",
            false,
            null
        ));

        // Oracle SYSDATE函数检测（不带括号）
        register(new ErrorPattern(
            "non_mysql_sysdate_no_parens",
            Pattern.compile("(?i)\\bSYSDATE(?!\\s*\\()\\b"),
            SqlErrorType.ORACLE_SYNTAX_ERROR,
            "MySQL语法错误：不支持 SYSDATE 函数（不带括号）",
            "根据MySQL 8.4官方文档，MySQL不支持Oracle风格的SYSDATE函数（不带括号）。",
            "请使用MySQL的NOW()或CURRENT_TIMESTAMP函数",
            false,
            null
        ));

        // 注意：DUAL表检测已被移除，因为MySQL 8.4官方文档明确支持此语法
        // 参考：https://dev.mysql.com/doc/en/select.html
        // "You are permitted to specify DUAL as a dummy table name in situations where no tables are referenced"

        // Oracle ROWNUM检测
        register(new ErrorPattern(
            "non_mysql_rownum",
            Pattern.compile("(?i)\\bROWNUM\\b"),
            SqlErrorType.ORACLE_SYNTAX_ERROR,
            "MySQL语法错误：不支持 ROWNUM 伪列",
            "根据MySQL 8.4官方文档，MySQL不支持Oracle的ROWNUM伪列。",
            "请使用MySQL的ROW_NUMBER()窗口函数或LIMIT子句",
            false,
            null
        ));

        // Oracle DECODE()函数检测
        register(new ErrorPattern(
            "non_mysql_decode",
            Pattern.compile("(?i)\\bDECODE\\s*\\("),
            SqlErrorType.ORACLE_SYNTAX_ERROR,
            "MySQL语法错误：不支持 DECODE() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持Oracle的DECODE()函数。",
            "请使用MySQL的CASE WHEN语句",
            false,
            null
        ));

        // PostgreSQL ILIKE操作符检测
        register(new ErrorPattern(
            "non_mysql_ilike",
            Pattern.compile("(?i)\\s+ILIKE\\s+"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 ILIKE 操作符",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL的ILIKE操作符（不区分大小写的LIKE）。",
            "请使用MySQL的LIKE操作符配合LOWER()或UPPER()函数",
            false,
            null
        ));

        // 注意：LIMIT OFFSET语法已被移除，因为MySQL 8.4官方文档明确支持此语法
        // 参考：https://dev.mysql.com/doc/en/select.html
        // "For compatibility with PostgreSQL, MySQL also supports the LIMIT row_count OFFSET offset syntax."

        // PostgreSQL BOOLEAN类型检测
        register(new ErrorPattern(
            "non_mysql_boolean_type",
            Pattern.compile("(?i)\\bBOOLEAN\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 BOOLEAN 数据类型",
            "根据MySQL 8.4官方文档，MySQL不支持BOOLEAN数据类型关键字。",
            "请使用MySQL的BOOL或TINYINT(1)数据类型",
            false,
            null
        ));

        // PostgreSQL generate_series()函数检测
        register(new ErrorPattern(
            "non_mysql_generate_series",
            Pattern.compile("(?i)\\bgenerate_series\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 generate_series() 函数",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL的generate_series()函数。",
            "请使用MySQL的递归CTE（WITH RECURSIVE）或其他方式生成序列",
            false,
            null
        ));

        // PostgreSQL EXTRACT语法差异检测（某些单位MySQL不支持）
        register(new ErrorPattern(
            "non_mysql_extract_units",
            Pattern.compile("(?i)\\bEXTRACT\\s*\\(\\s*(EPOCH|DOW|DOY|ISODOW|ISOYEAR|TIMEZONE|TIMEZONE_HOUR|TIMEZONE_MINUTE)\\s+FROM"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：EXTRACT函数不支持此时间单位",
            "根据MySQL 8.4官方文档，MySQL的EXTRACT函数不支持EPOCH、DOW、DOY、ISODOW、ISOYEAR、TIMEZONE等PostgreSQL特有的时间单位。",
            "请使用MySQL支持的时间单位：YEAR、MONTH、DAY、HOUR、MINUTE、SECOND、MICROSECOND等",
            false,
            null
        ));

        // SQL Server TOP语法检测
        register(new ErrorPattern(
            "non_mysql_top",
            Pattern.compile("(?i)\\bSELECT\\s+TOP\\s+\\d+\\b"),
            SqlErrorType.SQLSERVER_SYNTAX_ERROR,
            "MySQL语法错误：不支持 TOP 语法",
            "根据MySQL 8.4官方文档，MySQL不支持SQL Server的TOP语法。",
            "请使用MySQL的LIMIT子句",
            false,
            null
        ));

        // PostgreSQL DISTINCT ON语法检测
        register(new ErrorPattern(
            "non_mysql_distinct_on",
            Pattern.compile("(?i)\\bDISTINCT\\s+ON\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 DISTINCT ON 语法",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL的DISTINCT ON语法。",
            "请使用MySQL的GROUP BY或窗口函数实现相同功能",
            false,
            null
        ));

        // PostgreSQL FILTER子句检测（聚合函数中）
        register(new ErrorPattern(
            "non_mysql_filter_clause",
            Pattern.compile("(?i)\\)\\s+FILTER\\s*\\(\\s*WHERE"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 FILTER 子句",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL的FILTER子句。",
            "请使用MySQL的CASE WHEN语句在聚合函数内部实现条件聚合",
            false,
            null
        ));

        // PostgreSQL LATERAL JOIN检测
        register(new ErrorPattern(
            "non_mysql_lateral_join",
            Pattern.compile("(?i)\\bLATERAL\\s+"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "MySQL语法错误：不支持 LATERAL JOIN",
            "根据MySQL 8.4官方文档，MySQL不支持PostgreSQL的LATERAL JOIN语法。",
            "请使用MySQL的相关子查询或其他JOIN方式",
            false,
            null
        ));

        // PostgreSQL特有的聚合函数
        register(new ErrorPattern(
            "non_mysql_postgresql_aggregate",
            Pattern.compile("(?i)\\b(array_agg|string_agg)\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "PostgreSQL语法错误",
            "MySQL语法错误：不支持PostgreSQL聚合函数。根据MySQL 8.4官方文档，MySQL不支持 array_agg() 和 string_agg() 函数。",
            "请使用MySQL标准聚合函数：GROUP_CONCAT() 等",
            false,
            null
        ));

        // PostgreSQL特有的序列函数
        register(new ErrorPattern(
            "non_mysql_postgresql_sequence",
            Pattern.compile("(?i)\\bgenerate_series\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "PostgreSQL语法错误",
            "MySQL语法错误：不支持 generate_series() 函数。根据MySQL 8.4官方文档，MySQL不支持PostgreSQL的序列生成函数。",
            "请使用MySQL的递归CTE或其他方法生成序列",
            false,
            null
        ));

        // PostgreSQL特有的正则表达式函数
        register(new ErrorPattern(
            "non_mysql_postgresql_regex",
            Pattern.compile("(?i)\\bregexp_replace\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "PostgreSQL语法错误",
            "MySQL语法错误：不支持 regexp_replace() 函数。根据MySQL 8.4官方文档，MySQL使用 REGEXP_REPLACE() 函数（注意大小写）。",
            "请使用MySQL标准函数：REGEXP_REPLACE()",
            false,
            null
        ));

        // PostgreSQL特有的类型转换函数
        register(new ErrorPattern(
            "non_mysql_postgresql_cast",
            Pattern.compile("(?i)\\b(to_char|to_date|to_number)\\s*\\("),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "PostgreSQL语法错误",
            "MySQL语法错误：不支持PostgreSQL类型转换函数。根据MySQL 8.4官方文档，MySQL使用 CAST() 和 CONVERT() 函数进行类型转换。",
            "请使用MySQL标准函数：CAST() 或 CONVERT()",
            false,
            null
        ));

        // Oracle特有函数
        register(new ErrorPattern(
            "non_mysql_oracle_functions",
            Pattern.compile("(?i)\\b(nvl|nvl2|decode|rownum|sysdate|connect_by|start_with|prior)\\b"),
            SqlErrorType.ORACLE_SYNTAX_ERROR,
            "Oracle语法错误",
            "MySQL语法错误：使用了Oracle特有函数。根据MySQL 8.4官方文档，检测到的Oracle函数不符合MySQL规范。",
            "请使用MySQL对应的标准函数：IFNULL(), CASE WHEN, NOW() 等",
            false,
            null
        ));

        // SQL Server特有函数
        // 注意：DATEDIFF在MySQL和SQL Server中都存在但语法不同，这里只检测纯SQL Server函数
        // 根据MySQL 8.4官方文档，DATEDIFF()是MySQL标准函数，不应被误报
        register(new ErrorPattern(
            "non_mysql_sqlserver_functions",
            Pattern.compile("(?i)\\b(getdate|datepart|isnull)\\s*\\("),
            SqlErrorType.SQLSERVER_SYNTAX_ERROR,
            "SQL Server语法错误",
            "MySQL语法错误：使用了SQL Server特有函数。根据MySQL 8.4官方文档，检测到的SQL Server函数不符合MySQL规范。",
            "请使用MySQL对应的标准函数：NOW(), EXTRACT(), IFNULL() 等",
            false,
            null
        ));

        // 验证MySQL官方支持的数学函数（基于官方文档）
        registerMySqlMathFunctionValidation();
    }

    /**
     * 注册MySQL数学函数验证
     * 基于MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/mathematical-functions.html
     */
    private static void registerMySqlMathFunctionValidation() {
        // 添加对常见错误拼写的检测（不包括random，避免与PostgreSQL函数检测重叠）
        register(new ErrorPattern(
            "mysql_function_typo_rand",
            Pattern.compile("(?i)\\brnd\\s*\\("),
            SqlErrorType.FUNCTION_NAME_ERROR,
            "函数名错误",
            "MySQL语法错误：函数名拼写错误。根据MySQL 8.4官方文档，正确的随机数函数是 RAND()。",
            "请使用正确的函数名：RAND()",
            false,
            null
        ));

        // 注册MySQL日期时间函数验证
        registerMySqlDateTimeFunctionValidation();
    }

    /**
     * 注册MySQL日期时间函数验证
     * 基于MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
     */
    private static void registerMySqlDateTimeFunctionValidation() {
        // PostgreSQL特有的日期时间函数检测 - 修正：使用更精确的函数匹配
        register(new ErrorPattern(
            "non_mysql_postgresql_datetime",
            Pattern.compile("(?i)\\b(age\\s*\\(|date_trunc\\s*\\(|interval\\s+'\\d+\\s+\\w+')\\b"),
            SqlErrorType.POSTGRESQL_SYNTAX_ERROR,
            "PostgreSQL语法错误",
            "MySQL语法错误：使用了PostgreSQL特有的日期时间语法。根据MySQL 8.4官方文档，MySQL不支持这些PostgreSQL特有的日期时间函数或语法。",
            "请使用MySQL标准日期时间函数：NOW(), CURRENT_TIMESTAMP, DATE_ADD(), DATE_SUB(), EXTRACT() 等",
            false,
            null
        ));

        // Oracle特有的日期时间函数检测
        register(new ErrorPattern(
            "non_mysql_oracle_datetime",
            Pattern.compile("(?i)\\b(sysdate|add_months|months_between|next_day|last_day|trunc\\s*\\(.*,\\s*'\\w+'\\))\\b"),
            SqlErrorType.ORACLE_SYNTAX_ERROR,
            "Oracle语法错误",
            "MySQL语法错误：使用了Oracle特有的日期时间函数。根据MySQL 8.4官方文档，MySQL不支持这些Oracle特有的日期时间函数。",
            "请使用MySQL标准日期时间函数：NOW(), DATE_ADD(), DATE_SUB(), LAST_DAY() 等",
            false,
            null
        ));

        // SQL Server特有的日期时间函数检测
        // 注意：根据MySQL 8.4官方文档，YEAR()、MONTH()、DAY()、DATEDIFF()是MySQL标准函数，不应被拒绝
        // 参考：https://dev.mysql.com/doc/refman/8.4/en/date-and-time-functions.html
        register(new ErrorPattern(
            "non_mysql_sqlserver_datetime",
            Pattern.compile("(?i)\\b(getdate|getutcdate|dateadd|datepart|datename)\\s*\\("),
            SqlErrorType.SQLSERVER_SYNTAX_ERROR,
            "SQL Server语法错误",
            "MySQL语法错误：使用了SQL Server特有的日期时间函数。根据MySQL 8.4官方文档，MySQL有对应但语法不同的日期时间函数。",
            "请使用MySQL标准日期时间函数：NOW(), DATE_ADD(), DATEDIFF(), EXTRACT(), YEAR(), MONTH(), DAY() 等",
            false,
            null
        ));

        // 移除错误的CURRENT_TIMESTAMP检测模式
        // 根据 .augment/rules/rule-db.md 要求和MySQL 8.4官方文档，
        // CURRENT_TIMESTAMP(fsp) 是正确的MySQL语法，其中fsp是0-6的精度参数
    }

    /**
     * 验证MySQL语法规范
     * 根据 .augment/rules/rule-db.md 要求，严格遵循MySQL官方规范
     * 结合ANTLR解析器和官方函数白名单进行全面校验
     *
     * @param sql 待验证的SQL语句
     * @return 验证结果
     */
    public static MySqlSyntaxValidationResult validateMySqlSyntax(String sql) {
        MySqlSyntaxValidationResult result = new MySqlSyntaxValidationResult();

        if (sql == null || sql.trim().isEmpty()) {
            log.debug("开始强制性MySQL语法验证: <空SQL>");
            return result;
        }

        log.debug("开始强制性MySQL语法验证: {}", sql.substring(0, Math.min(sql.length(), 100)));

        // 第一步：优先检测明确的非MySQL语法错误（在ANTLR解析之前）
        // 根据 .augment/rules/rule-db.md 要求，只拒绝非MySQL语法，不拒绝正确的MySQL语法
        List<ErrorPattern> matchedPatterns = new ArrayList<>();
        for (ErrorPattern pattern : ERROR_PATTERNS) {
            if (pattern.matchesSql(sql) && isNonMySqlSyntaxPattern(pattern)) {
                matchedPatterns.add(pattern);
            }
        }

        // 报告所有违规项但进行智能去重（避免同一语法问题的重复报告）
        List<ErrorPattern> sortedPatterns = sortErrorPatternsByPriority(matchedPatterns);
        List<ErrorPattern> deduplicatedPatterns = deduplicateErrorPatterns(sortedPatterns, sql);
        
        for (ErrorPattern pattern : deduplicatedPatterns) {
            MySqlSyntaxViolation violation = new MySqlSyntaxViolation(
                pattern.getId(),
                pattern.getFriendlyMessage(),
                pattern.getDetailedMessage(),
                pattern.getSuggestion(),
                pattern.getErrorType()
            );
            result.addViolation(violation);

            // 记录检测到的非MySQL语法
            log.warn("检测到非MySQL语法: {} - {}", pattern.getId(), pattern.getFriendlyMessage());
        }
        
        if (deduplicatedPatterns.size() < sortedPatterns.size()) {
            log.debug("智能去重：从 {} 个匹配模式中筛选出 {} 个独特违规项", 
                     sortedPatterns.size(), deduplicatedPatterns.size());
        }

        // 如果已经检测到明确的非MySQL语法错误，直接返回，避免ANTLR解析失败
        if (result.hasViolations()) {
            log.debug("检测到明确的非MySQL语法，跳过ANTLR解析");
            return result;
        }

        // 第二步：基于ANTLR的严格MySQL语法解析校验（仅在没有明确语法错误时执行）
        MySqlSyntaxValidationResult antlrResult = MySqlStrictSyntaxValidator.validateWithAntlrParser(sql);
        for (MySqlSyntaxViolation violation : antlrResult.getViolations()) {
            result.addViolation(violation);
        }

        // 第三步：基于MySQL官方函数白名单的校验
        MySqlSyntaxValidationResult functionResult = MySqlStrictSyntaxValidator.validateMySqlFunctions(sql);
        for (MySqlSyntaxViolation violation : functionResult.getViolations()) {
            result.addViolation(violation);
        }

        return result;
    }

    /**
     * 智能去重错误模式，避免同一语法问题的重复报告
     * 例如：random()函数同时匹配PostgreSQL函数和函数拼写错误，只保留更权威的PostgreSQL函数错误
     * 
     * @param sortedPatterns 已按优先级排序的错误模式列表
     * @param sql 原始SQL语句
     * @return 去重后的错误模式列表
     */
    private static List<ErrorPattern> deduplicateErrorPatterns(List<ErrorPattern> sortedPatterns, String sql) {
        if (sortedPatterns.size() <= 1) {
            return sortedPatterns;
        }
        
        List<ErrorPattern> deduplicated = new ArrayList<>();
        Set<String> coveredTokens = new HashSet<>();
        
        for (ErrorPattern pattern : sortedPatterns) {
            // 提取该模式匹配的关键词/标记
            Set<String> patternTokens = extractPatternTokens(pattern, sql);
            
            // 检查是否与已添加的模式有重叠
            boolean hasOverlap = false;
            for (String token : patternTokens) {
                if (coveredTokens.contains(token)) {
                    hasOverlap = true;
                    break;
                }
            }
            
            // 如果没有重叠，添加到结果中
            if (!hasOverlap) {
                deduplicated.add(pattern);
                coveredTokens.addAll(patternTokens);
            }
        }
        
        return deduplicated;
    }
    
    /**
     * 提取错误模式匹配的关键词/标记
     */
    private static Set<String> extractPatternTokens(ErrorPattern pattern, String sql) {
        Set<String> tokens = new HashSet<>();
        
        // 根据模式ID识别关键词，更精确地识别重叠模式
        String patternId = pattern.getId();
        if (patternId.contains("random")) {
            tokens.add("random");
        } else if (patternId.contains("generate_series")) {
            tokens.add("generate_series");
        } else if (patternId.contains("ilike")) {
            tokens.add("ilike");
            // ILIKE和PostgreSQL操作符可能重叠
            tokens.add("postgresql_operators");
        } else if (patternId.contains("returning")) {
            tokens.add("returning");
        } else if (patternId.contains("cast") || patternId.contains("type_cast")) {
            tokens.add("type_cast");
        } else if (patternId.contains("postgresql_sequence")) {
            tokens.add("postgresql_sequence");
            // PostgreSQL序列和generate_series可能重叠
            tokens.add("generate_series");
        } else if (patternId.contains("postgresql_operators")) {
            tokens.add("postgresql_operators");
            // PostgreSQL操作符和ILIKE可能重叠
            tokens.add("ilike");
        } else {
            // 对于其他模式，使用模式ID作为唯一标识
            tokens.add(patternId);
        }
        
        return tokens;
    }

    /**
     * 判断错误模式是否为真正的非MySQL语法模式
     * 根据 .augment/rules/rule-db.md 要求，只拒绝非MySQL语法，不拒绝正确的MySQL语法
     *
     * @param pattern 错误模式
     * @return true如果是非MySQL语法，false如果是正确的MySQL语法（即使是MySQL特有的）
     */
    private static boolean isNonMySqlSyntaxPattern(ErrorPattern pattern) {
        // 只拒绝明确的非MySQL语法错误
        return pattern.getErrorType() == SqlErrorType.POSTGRESQL_SYNTAX_ERROR ||
               pattern.getErrorType() == SqlErrorType.ORACLE_SYNTAX_ERROR ||
               pattern.getErrorType() == SqlErrorType.SQLSERVER_SYNTAX_ERROR ||
               pattern.getErrorType() == SqlErrorType.FUNCTION_NAME_ERROR ||
               pattern.getErrorType() == SqlErrorType.GENERIC_SYNTAX_ERROR ||
               // 排除MySQL特有但正确的语法
               (pattern.getErrorType() != SqlErrorType.MYSQL_SPECIFIC_SYNTAX &&
                pattern.getErrorType() != SqlErrorType.DATA_TYPE_COMPATIBILITY &&
                pattern.getErrorType() != SqlErrorType.INVALID_KEYWORD_USAGE &&
                (pattern.getId().startsWith("non_mysql_") ||
                 pattern.getId().contains("postgresql_") ||
                 pattern.getId().contains("oracle_") ||
                 pattern.getId().contains("sqlserver_")));
    }

    /**
     * 按优先级排序错误模式（提供完整的错误信息）
     * 优先级：明确的数据库语法错误 > 函数名错误 > 通用错误
     *
     * @param matchedPatterns 匹配的错误模式列表
     * @return 按优先级排序的错误模式列表
     */
    private static List<ErrorPattern> sortErrorPatternsByPriority(List<ErrorPattern> matchedPatterns) {
        if (matchedPatterns.isEmpty()) {
            return new ArrayList<>();
        }

        List<ErrorPattern> sortedPatterns = new ArrayList<>(matchedPatterns);

        // 按优先级排序
        sortedPatterns.sort((p1, p2) -> {
            int priority1 = getErrorTypePriority(p1.getErrorType());
            int priority2 = getErrorTypePriority(p2.getErrorType());
            return Integer.compare(priority1, priority2);
        });

        return sortedPatterns;
    }

    /**
     * 获取错误类型的优先级（数字越小优先级越高）
     */
    private static int getErrorTypePriority(SqlErrorType errorType) {
        switch (errorType) {
            case POSTGRESQL_SYNTAX_ERROR:
            case ORACLE_SYNTAX_ERROR:
            case SQLSERVER_SYNTAX_ERROR:
                return 1; // 最高优先级：明确的数据库语法错误
            case FUNCTION_NAME_ERROR:
                return 2; // 中等优先级：函数名错误
            case GENERIC_SYNTAX_ERROR:
                return 3; // 较低优先级：通用语法错误
            default:
                return 4; // 最低优先级：其他错误
        }
    }


}
