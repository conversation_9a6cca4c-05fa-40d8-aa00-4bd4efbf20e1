package com.xylink.sqltranspiler.infrastructure.parser.error;

import java.util.ArrayList;
import java.util.List;

/**
 * MySQL语法验证结果
 * 根据 .augment/rules/rule-db.md 要求，严格遵循MySQL官方规范
 * 
 * <AUTHOR> Transpiler Team
 */
public class MySqlSyntaxValidationResult {
    
    private final List<MySqlSyntaxViolation> violations = new ArrayList<>();
    private boolean isValid = true;
    
    /**
     * 添加语法违规
     */
    public void addViolation(MySqlSyntaxViolation violation) {
        // 检查是否已存在相同的违规，避免重复添加
        if (!isDuplicateViolation(violation)) {
            violations.add(violation);
            isValid = false;
        }
    }

    /**
     * 检查是否为重复的违规
     */
    private boolean isDuplicateViolation(MySqlSyntaxViolation newViolation) {
        for (MySqlSyntaxViolation existing : violations) {
            // 如果错误类型、友好消息和详细消息都相同，则认为是重复的
            if (existing.getErrorType() == newViolation.getErrorType() &&
                existing.getFriendlyMessage().equals(newViolation.getFriendlyMessage()) &&
                existing.getDetailedMessage().equals(newViolation.getDetailedMessage())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * 是否符合MySQL语法规范
     */
    public boolean isValid() {
        return isValid;
    }
    
    /**
     * 是否有语法违规
     */
    public boolean hasViolations() {
        return !violations.isEmpty();
    }
    
    /**
     * 获取所有语法违规
     */
    public List<MySqlSyntaxViolation> getViolations() {
        return new ArrayList<>(violations);
    }
    
    /**
     * 获取违规数量
     */
    public int getViolationCount() {
        return violations.size();
    }
    
    /**
     * 获取第一个违规（如果存在）
     */
    public MySqlSyntaxViolation getFirstViolation() {
        return violations.isEmpty() ? null : violations.get(0);
    }

    /**
     * 合并另一个验证结果
     * 根据 .augment/rules/rule-db.md 要求，支持多种验证方式的结果合并
     *
     * @param other 另一个验证结果
     */
    public void mergeWith(MySqlSyntaxValidationResult other) {
        if (other != null && other.hasViolations()) {
            this.violations.addAll(other.getViolations());
            this.isValid = false;
        }
    }
    
    /**
     * 生成详细的错误报告
     */
    public String generateErrorReport() {
        if (isValid) {
            return "SQL语句符合MySQL官方规范";
        }
        
        StringBuilder report = new StringBuilder();
        report.append("MySQL语法验证失败，发现 ").append(violations.size()).append(" 个违规:\n\n");
        
        for (int i = 0; i < violations.size(); i++) {
            MySqlSyntaxViolation violation = violations.get(i);
            report.append(String.format("%d. %s\n", i + 1, violation.getFriendlyMessage()));
            report.append(String.format("   详细说明: %s\n", violation.getDetailedMessage()));
            report.append(String.format("   修复建议: %s\n", violation.getSuggestion()));
            report.append(String.format("   错误类型: %s\n", violation.getErrorType()));
            if (i < violations.size() - 1) {
                report.append("\n");
            }
        }
        
        return report.toString();
    }
    
    @Override
    public String toString() {
        return String.format("MySqlSyntaxValidationResult{valid=%s, violations=%d}", 
                           isValid, violations.size());
    }
}
