package com.xylink.sqltranspiler.infrastructure.parser.error;

import lombok.extern.slf4j.Slf4j;
import org.antlr.v4.runtime.*;
import org.antlr.v4.runtime.atn.ATNState;
import org.antlr.v4.runtime.atn.RuleStartState;
import org.antlr.v4.runtime.misc.IntervalSet;

import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * ANTLR语法树错误分析器
 * 专门处理无法通过模式匹配识别的复杂语法错误
 * 根据 .augment/rules/rule-db.md 要求，基于官方文档提供准确的错误分析
 * 
 * <AUTHOR> Transpiler Team
 */
@Slf4j
public class AntlrSyntaxTreeAnalyzer {
    
    /**
     * 分析ANTLR语法错误并提供增强的错误信息
     * 
     * @param recognizer ANTLR识别器
     * @param offendingSymbol 错误符号
     * @param line 错误行号
     * @param charPositionInLine 错误列号
     * @param msg 原始错误消息
     * @param e 识别异常
     * @param originalSql 原始SQL
     * @return 增强的错误信息
     */
    public static SqlErrorInfo analyzeAntlrError(Recognizer<?, ?> recognizer, 
                                               Object offendingSymbol,
                                               int line, 
                                               int charPositionInLine, 
                                               String msg, 
                                               RecognitionException e,
                                               String originalSql) {
        
        SqlErrorInfo errorInfo = new SqlErrorInfo();
        errorInfo.setOriginalError(msg);
        errorInfo.setLine(line);
        errorInfo.setPosition(charPositionInLine);
        
        // 分析错误类型并生成详细信息
        analyzeErrorType(msg, errorInfo);
        
        // 分析期望的语法结构
        analyzeExpectedSyntax(recognizer, e, errorInfo);
        
        // 分析错误上下文
        analyzeErrorContext(originalSql, line, charPositionInLine, errorInfo);
        
        // 生成修复建议
        generateRepairSuggestions(originalSql, line, charPositionInLine, msg, errorInfo);
        
        log.debug("ANTLR错误分析完成: {} -> {}", msg, errorInfo.getFriendlyMessage());
        
        return errorInfo;
    }
    
    /**
     * 分析错误类型
     */
    private static void analyzeErrorType(String msg, SqlErrorInfo errorInfo) {
        if (msg.contains("no viable alternative")) {
            errorInfo.setErrorType(SqlErrorType.GENERIC_SYNTAX_ERROR);
            errorInfo.setFriendlyMessage("语法错误：在当前位置无法识别有效的语法结构");
        } else if (msg.contains("missing")) {
            errorInfo.setErrorType(SqlErrorType.MISSING_TOKEN);
            errorInfo.setFriendlyMessage("语法错误：缺少必需的语法元素");
        } else if (msg.contains("mismatched input")) {
            errorInfo.setErrorType(SqlErrorType.MISMATCHED_INPUT);
            errorInfo.setFriendlyMessage("语法错误：输入的内容与期望的语法不匹配");
        } else if (msg.contains("extraneous input")) {
            errorInfo.setErrorType(SqlErrorType.UNEXPECTED_TOKEN);
            errorInfo.setFriendlyMessage("语法错误：发现多余的输入内容");
        } else {
            errorInfo.setErrorType(SqlErrorType.GENERIC_SYNTAX_ERROR);
            errorInfo.setFriendlyMessage("SQL语法错误");
        }
    }
    
    /**
     * 分析期望的语法结构
     */
    private static void analyzeExpectedSyntax(Recognizer<?, ?> recognizer, 
                                            RecognitionException e, 
                                            SqlErrorInfo errorInfo) {
        if (recognizer == null || e == null) {
            return;
        }
        
        try {
            // 获取期望的标记
            IntervalSet expectedTokens = recognizer.getATN().getExpectedTokens(
                e.getOffendingState(), e.getCtx());
            
            if (expectedTokens != null && !expectedTokens.isNil()) {
                List<String> expectedNames = new ArrayList<>();
                for (int token : expectedTokens.toArray()) {
                    String tokenName = recognizer.getVocabulary().getDisplayName(token);
                    if (tokenName != null && !tokenName.equals("<INVALID>")) {
                        expectedNames.add(tokenName);
                    }
                }
                
                if (!expectedNames.isEmpty()) {
                    String expectedSyntax = generateExpectedSyntaxDescription(expectedNames);
                    errorInfo.setSuggestion(errorInfo.getSuggestion() + "\n\n期望的语法元素：" + expectedSyntax);
                }
            }
        } catch (Exception ex) {
            log.debug("无法分析期望的语法结构: {}", ex.getMessage());
        }
    }
    
    /**
     * 生成期望语法的描述
     */
    private static String generateExpectedSyntaxDescription(List<String> expectedNames) {
        StringBuilder sb = new StringBuilder();
        
        // 对期望的标记进行分类和友好化处理
        List<String> keywords = new ArrayList<>();
        List<String> operators = new ArrayList<>();
        List<String> identifiers = new ArrayList<>();
        List<String> literals = new ArrayList<>();
        
        for (String name : expectedNames) {
            String cleanName = name.replace("'", "");
            if (isKeyword(cleanName)) {
                keywords.add(cleanName.toUpperCase());
            } else if (isOperator(cleanName)) {
                operators.add(cleanName);
            } else if (isIdentifier(cleanName)) {
                identifiers.add("标识符");
            } else if (isLiteral(cleanName)) {
                literals.add("字面值");
            } else {
                // 其他情况，直接添加
                if (!cleanName.isEmpty()) {
                    keywords.add(cleanName);
                }
            }
        }
        
        // 构建友好的描述
        List<String> descriptions = new ArrayList<>();
        if (!keywords.isEmpty()) {
            descriptions.add("关键字: " + String.join(", ", keywords));
        }
        if (!operators.isEmpty()) {
            descriptions.add("操作符: " + String.join(", ", operators));
        }
        if (!identifiers.isEmpty()) {
            descriptions.add("标识符（表名、列名等）");
        }
        if (!literals.isEmpty()) {
            descriptions.add("字面值（数字、字符串等）");
        }
        
        return String.join("; ", descriptions);
    }
    
    /**
     * 分析错误上下文
     */
    private static void analyzeErrorContext(String originalSql, int line, int position, SqlErrorInfo errorInfo) {
        if (originalSql == null || originalSql.isEmpty()) {
            return;
        }
        
        String[] lines = originalSql.split("\n");
        if (line <= 0 || line > lines.length) {
            return;
        }
        
        String errorLine = lines[line - 1];
        
        // 分析错误位置的上下文
        String contextAnalysis = analyzeLocalContext(errorLine, position);
        if (contextAnalysis != null) {
            String currentDetail = errorInfo.getDetailedMessage();
            errorInfo.setDetailedMessage(
                (currentDetail != null ? currentDetail : "") + 
                "\n\n上下文分析：" + contextAnalysis
            );
        }
    }
    
    /**
     * 分析局部上下文
     */
    private static String analyzeLocalContext(String line, int position) {
        if (line == null || position < 0) {
            return null;
        }
        
        StringBuilder analysis = new StringBuilder();
        
        // 分析错误位置前的内容
        String beforeError = position < line.length() ? line.substring(0, position) : line;
        String afterError = position < line.length() ? line.substring(position) : "";
        
        // 检查是否在字符串中
        if (isInsideString(beforeError)) {
            analysis.append("错误位置在字符串内部，可能是未闭合的引号。");
        }
        
        // 检查是否缺少分隔符
        if (needsCommaOrSemicolon(beforeError)) {
            analysis.append("可能缺少逗号(,)或分号(;)。");
        }
        
        // 检查括号匹配
        String bracketIssue = checkBracketMatching(beforeError);
        if (bracketIssue != null) {
            analysis.append(bracketIssue);
        }
        
        return analysis.length() > 0 ? analysis.toString() : null;
    }
    
    /**
     * 生成修复建议
     */
    private static void generateRepairSuggestions(String originalSql, int line, int position, 
                                                String msg, SqlErrorInfo errorInfo) {
        List<String> suggestions = new ArrayList<>();
        
        // 基于错误消息生成建议
        if (msg.contains("no viable alternative")) {
            suggestions.add("检查语法结构是否正确，参考MySQL官方文档");
            suggestions.add("确认关键字拼写是否正确");
            suggestions.add("检查是否缺少必要的标点符号");
        } else if (msg.contains("missing")) {
            if (msg.contains("';'")) {
                suggestions.add("在SQL语句末尾添加分号(;)");
            } else if (msg.contains("'('")) {
                suggestions.add("添加缺少的左括号(");
            } else if (msg.contains("')'")) {
                suggestions.add("添加缺少的右括号)");
            } else {
                suggestions.add("添加缺少的语法元素");
            }
        }
        
        // 基于上下文生成建议
        if (originalSql != null) {
            String contextSuggestion = generateContextBasedSuggestion(originalSql, line, position);
            if (contextSuggestion != null) {
                suggestions.add(contextSuggestion);
            }
        }
        
        // 添加语法示例
        String syntaxExample = MySqlSyntaxExampleGenerator.generateSyntaxExample(originalSql, msg, line, position);
        if (syntaxExample != null) {
            suggestions.add("语法示例：\n" + syntaxExample);
        }

        // 添加通用建议
        suggestions.add("参考MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/");

        String combinedSuggestion = String.join("\n• ", suggestions);
        errorInfo.setSuggestion("• " + combinedSuggestion);
    }
    
    /**
     * 基于上下文生成建议
     */
    private static String generateContextBasedSuggestion(String sql, int line, int position) {
        // 这里可以添加更复杂的上下文分析逻辑
        // 例如：检测常见的SQL模式，提供针对性建议
        
        if (sql.toUpperCase().contains("ALTER TABLE") && sql.toUpperCase().contains("ADD")) {
            return "对于ALTER TABLE ADD语句，请确保语法为：ALTER TABLE table_name ADD column_name data_type";
        }
        
        if (sql.toUpperCase().contains("ON UPDATE") && sql.toUpperCase().contains("NOW()")) {
            return "MySQL中ON UPDATE子句应使用CURRENT_TIMESTAMP而不是NOW()";
        }
        
        return null;
    }
    
    // 辅助方法
    private static boolean isKeyword(String token) {
        String[] keywords = {"SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", 
                           "CREATE", "ALTER", "DROP", "TABLE", "INDEX", "ADD", "COLUMN"};
        return Arrays.asList(keywords).contains(token.toUpperCase());
    }
    
    private static boolean isOperator(String token) {
        return token.matches("[+\\-*/=<>!,;()\\[\\]{}]");
    }
    
    private static boolean isIdentifier(String token) {
        return token.matches("IDENTIFIER|ID|NAME");
    }
    
    private static boolean isLiteral(String token) {
        return token.matches("STRING|NUMBER|LITERAL");
    }
    
    private static boolean isInsideString(String text) {
        int singleQuotes = 0;
        int doubleQuotes = 0;
        for (char c : text.toCharArray()) {
            if (c == '\'') singleQuotes++;
            if (c == '"') doubleQuotes++;
        }
        return (singleQuotes % 2 != 0) || (doubleQuotes % 2 != 0);
    }
    
    private static boolean needsCommaOrSemicolon(String text) {
        return text.trim().endsWith(")")  && !text.trim().endsWith(";");
    }
    
    private static String checkBracketMatching(String text) {
        int parentheses = 0;
        int brackets = 0;
        int braces = 0;
        
        for (char c : text.toCharArray()) {
            switch (c) {
                case '(': parentheses++; break;
                case ')': parentheses--; break;
                case '[': brackets++; break;
                case ']': brackets--; break;
                case '{': braces++; break;
                case '}': braces--; break;
            }
        }
        
        if (parentheses > 0) return "可能缺少右括号)。";
        if (parentheses < 0) return "可能有多余的右括号)。";
        if (brackets > 0) return "可能缺少右方括号]。";
        if (brackets < 0) return "可能有多余的右方括号]。";
        if (braces > 0) return "可能缺少右大括号}。";
        if (braces < 0) return "可能有多余的右大括号}。";
        
        return null;
    }
}
