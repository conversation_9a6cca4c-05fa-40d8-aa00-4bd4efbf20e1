package com.xylink.sqltranspiler.infrastructure.parser.error;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.regex.Pattern;

/**
 * MySQL语法示例生成器
 * 根据错误上下文生成正确的MySQL语法示例
 * 基于MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/
 * 
 * <AUTHOR> Transpiler Team
 */
@Slf4j
public class MySqlSyntaxExampleGenerator {
    
    /**
     * 根据SQL上下文和错误信息生成语法示例
     */
    public static String generateSyntaxExample(String originalSql, String errorMessage, int line, int position) {
        if (originalSql == null || originalSql.trim().isEmpty()) {
            return null;
        }
        
        String upperSql = originalSql.toUpperCase();
        
        // 根据SQL类型生成相应的语法示例
        if (upperSql.contains("ALTER TABLE")) {
            return generateAlterTableExample(originalSql, errorMessage);
        } else if (upperSql.contains("CREATE TABLE")) {
            return generateCreateTableExample(originalSql, errorMessage);
        } else if (upperSql.contains("INSERT INTO")) {
            return generateInsertExample(originalSql, errorMessage);
        } else if (upperSql.contains("UPDATE")) {
            return generateUpdateExample(originalSql, errorMessage);
        } else if (upperSql.contains("SELECT")) {
            return generateSelectExample(originalSql, errorMessage);
        } else if (upperSql.contains("DELETE")) {
            return generateDeleteExample(originalSql, errorMessage);
        }
        
        return generateGenericExample(errorMessage);
    }
    
    /**
     * 生成ALTER TABLE语法示例
     */
    private static String generateAlterTableExample(String sql, String errorMessage) {
        StringBuilder example = new StringBuilder();
        example.append("正确的ALTER TABLE语法示例：\n\n");
        
        if (sql.toUpperCase().contains("ADD") && sql.toUpperCase().contains("COLUMN")) {
            example.append("-- 添加列\n");
            example.append("ALTER TABLE table_name ADD COLUMN column_name data_type [constraints];\n\n");
            example.append("-- 示例：\n");
            example.append("ALTER TABLE users ADD COLUMN email VARCHAR(255) NOT NULL;\n");
            example.append("ALTER TABLE users ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;\n");
        } else if (sql.toUpperCase().contains("ADD") && !sql.toUpperCase().contains("COLUMN")) {
            example.append("-- 添加列（COLUMN关键字可选）\n");
            example.append("ALTER TABLE table_name ADD column_name data_type [constraints];\n\n");
            example.append("-- 示例：\n");
            example.append("ALTER TABLE users ADD email VARCHAR(255) NOT NULL;\n");
        }
        
        if (sql.toUpperCase().contains("ON UPDATE")) {
            example.append("\n-- ON UPDATE子句正确语法：\n");
            example.append("ALTER TABLE table_name ADD COLUMN update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;\n");
            example.append("-- 注意：使用CURRENT_TIMESTAMP而不是NOW()\n");
        }
        
        return example.toString();
    }
    
    /**
     * 生成CREATE TABLE语法示例
     */
    private static String generateCreateTableExample(String sql, String errorMessage) {
        StringBuilder example = new StringBuilder();
        example.append("正确的CREATE TABLE语法示例：\n\n");
        
        example.append("CREATE TABLE table_name (\n");
        example.append("    id INT AUTO_INCREMENT PRIMARY KEY,\n");
        example.append("    name VARCHAR(255) NOT NULL,\n");
        example.append("    email VARCHAR(255) UNIQUE,\n");
        example.append("    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n");
        example.append("    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP\n");
        example.append(");\n");
        
        return example.toString();
    }
    
    /**
     * 生成INSERT语法示例
     */
    private static String generateInsertExample(String sql, String errorMessage) {
        StringBuilder example = new StringBuilder();
        example.append("正确的INSERT语法示例：\n\n");
        
        example.append("-- 基本INSERT语法\n");
        example.append("INSERT INTO table_name (column1, column2, column3) VALUES (value1, value2, value3);\n\n");
        
        example.append("-- 多行INSERT\n");
        example.append("INSERT INTO table_name (column1, column2) VALUES \n");
        example.append("    (value1, value2),\n");
        example.append("    (value3, value4),\n");
        example.append("    (value5, value6);\n");
        
        return example.toString();
    }
    
    /**
     * 生成UPDATE语法示例
     */
    private static String generateUpdateExample(String sql, String errorMessage) {
        StringBuilder example = new StringBuilder();
        example.append("正确的UPDATE语法示例：\n\n");
        
        example.append("-- 基本UPDATE语法\n");
        example.append("UPDATE table_name SET column1 = value1, column2 = value2 WHERE condition;\n\n");
        
        example.append("-- 带JOIN的UPDATE\n");
        example.append("UPDATE table1 t1 JOIN table2 t2 ON t1.id = t2.id \n");
        example.append("SET t1.column1 = t2.column1 WHERE t2.condition = 'value';\n");
        
        return example.toString();
    }
    
    /**
     * 生成SELECT语法示例
     */
    private static String generateSelectExample(String sql, String errorMessage) {
        StringBuilder example = new StringBuilder();
        example.append("正确的SELECT语法示例：\n\n");
        
        example.append("-- 基本SELECT语法\n");
        example.append("SELECT column1, column2 FROM table_name WHERE condition;\n\n");
        
        example.append("-- 带JOIN的SELECT\n");
        example.append("SELECT t1.column1, t2.column2 \n");
        example.append("FROM table1 t1 \n");
        example.append("JOIN table2 t2 ON t1.id = t2.foreign_id \n");
        example.append("WHERE t1.condition = 'value';\n");
        
        return example.toString();
    }
    
    /**
     * 生成DELETE语法示例
     */
    private static String generateDeleteExample(String sql, String errorMessage) {
        StringBuilder example = new StringBuilder();
        example.append("正确的DELETE语法示例：\n\n");
        
        example.append("-- 基本DELETE语法\n");
        example.append("DELETE FROM table_name WHERE condition;\n\n");
        
        example.append("-- 带JOIN的DELETE\n");
        example.append("DELETE t1 FROM table1 t1 \n");
        example.append("JOIN table2 t2 ON t1.id = t2.foreign_id \n");
        example.append("WHERE t2.condition = 'value';\n");
        
        return example.toString();
    }
    
    /**
     * 生成通用语法示例
     */
    private static String generateGenericExample(String errorMessage) {
        StringBuilder example = new StringBuilder();
        example.append("常见MySQL语法要点：\n\n");
        
        if (errorMessage.contains("missing") && errorMessage.contains("';'")) {
            example.append("• 每个SQL语句必须以分号(;)结尾\n");
        }
        
        if (errorMessage.contains("missing") && errorMessage.contains("'('")) {
            example.append("• 检查是否缺少左括号(\n");
        }
        
        if (errorMessage.contains("missing") && errorMessage.contains("')'")) {
            example.append("• 检查是否缺少右括号)\n");
        }
        
        example.append("• 关键字不区分大小写，但建议使用大写\n");
        example.append("• 标识符（表名、列名）可以用反引号(`)包围\n");
        example.append("• 字符串值必须用单引号(')或双引号(\")包围\n");
        example.append("• 注释使用 -- 或 /* */\n");
        
        example.append("\n参考MySQL 8.4官方文档：https://dev.mysql.com/doc/refman/8.4/en/");
        
        return example.toString();
    }
    
    /**
     * 生成数据类型示例
     */
    public static String generateDataTypeExamples() {
        StringBuilder example = new StringBuilder();
        example.append("MySQL 8.4常用数据类型：\n\n");
        
        example.append("-- 数值类型\n");
        example.append("INT, BIGINT, DECIMAL(10,2), FLOAT, DOUBLE\n\n");
        
        example.append("-- 字符串类型\n");
        example.append("VARCHAR(255), CHAR(10), TEXT, LONGTEXT\n\n");
        
        example.append("-- 日期时间类型\n");
        example.append("DATE, TIME, DATETIME, TIMESTAMP\n\n");
        
        example.append("-- JSON类型\n");
        example.append("JSON\n\n");
        
        example.append("-- 二进制类型\n");
        example.append("BLOB, LONGBLOB\n");
        
        return example.toString();
    }
    
    /**
     * 生成约束示例
     */
    public static String generateConstraintExamples() {
        StringBuilder example = new StringBuilder();
        example.append("MySQL约束示例：\n\n");
        
        example.append("-- 主键约束\n");
        example.append("id INT AUTO_INCREMENT PRIMARY KEY\n\n");
        
        example.append("-- 外键约束\n");
        example.append("FOREIGN KEY (user_id) REFERENCES users(id)\n\n");
        
        example.append("-- 唯一约束\n");
        example.append("email VARCHAR(255) UNIQUE\n\n");
        
        example.append("-- 非空约束\n");
        example.append("name VARCHAR(255) NOT NULL\n\n");
        
        example.append("-- 检查约束（MySQL 8.0.16+）\n");
        example.append("age INT CHECK (age >= 0 AND age <= 150)\n\n");
        
        example.append("-- 默认值\n");
        example.append("status VARCHAR(20) DEFAULT 'active'\n");
        example.append("created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP\n");
        
        return example.toString();
    }
}
