package com.xylink.sqltranspiler.infrastructure.parser.error;

import java.util.Optional;

import lombok.extern.slf4j.Slf4j;

/**
 * 增强的SQL错误分析器
 * 作为错误分析的协调器，使用 ErrorPatternRegistry 进行模式匹配
 * 负责组装和格式化错误信息
 */
@Slf4j
public class EnhancedErrorAnalyzer {

    /**
     * 分析SQL语法错误并提供友好的错误信息
     * 使用注册表模式进行更通用的错误处理
     */
    public static SqlErrorInfo analyzeSqlError(String originalSql, String errorMessage, int line, int position) {
        log.debug("Analyzing SQL error: {} at line {}, position {}", errorMessage, line, position);

        SqlErrorInfo errorInfo = new SqlErrorInfo();
        errorInfo.setOriginalError(errorMessage);
        errorInfo.setLine(line);
        errorInfo.setPosition(position);
        errorInfo.setErrorType(SqlErrorType.GENERIC_SYNTAX_ERROR);

        // 使用注册表查找匹配的错误模式
        Optional<ErrorPatternRegistry.ErrorPattern> matchingPattern =
            ErrorPatternRegistry.findMatchingPattern(originalSql, errorMessage);

        if (matchingPattern.isPresent()) {
            ErrorPatternRegistry.ErrorPattern pattern = matchingPattern.get();

            errorInfo.setErrorType(pattern.getErrorType());
            errorInfo.setFriendlyMessage(pattern.getFriendlyMessage());
            errorInfo.setDetailedMessage(pattern.getDetailedMessage());
            errorInfo.setSuggestion(pattern.getSuggestion());
            errorInfo.setAutoFixable(pattern.isAutoFixable());

            // 如果有自动修复功能，生成修复后的SQL
            if (pattern.isAutoFixable() && pattern.getAutoFixer() != null) {
                String fixedSql = pattern.getAutoFixer().apply(originalSql);
                errorInfo.setSuggestedFix(fixedSql);
            }

            log.debug("Applied error pattern: {} for error type: {}", pattern.getId(), pattern.getErrorType());
            return errorInfo;
        }

        // 如果没有匹配的模式，使用增强的通用错误处理
        analyzeGenericError(originalSql, errorMessage, line, position, errorInfo);
        return errorInfo;
    }

    /**
     * 分析ANTLR语法错误并提供增强的错误信息
     * 专门处理复杂的语法树解析错误
     */
    public static SqlErrorInfo analyzeAntlrError(org.antlr.v4.runtime.Recognizer<?, ?> recognizer,
                                               Object offendingSymbol,
                                               int line,
                                               int charPositionInLine,
                                               String msg,
                                               org.antlr.v4.runtime.RecognitionException e,
                                               String originalSql) {

        log.debug("Analyzing ANTLR error: {} at line {}, position {}", msg, line, charPositionInLine);

        // 首先尝试使用错误模式匹配
        SqlErrorInfo errorInfo = analyzeSqlError(originalSql, msg, line, charPositionInLine);

        // 如果没有找到匹配的模式或只是通用错误，使用ANTLR语法树分析器增强
        if (errorInfo.getErrorType() == SqlErrorType.GENERIC_SYNTAX_ERROR &&
            (errorInfo.getFriendlyMessage() == null ||
             errorInfo.getFriendlyMessage().equals("SQL语法错误") ||
             errorInfo.getFriendlyMessage().equals("语法错误：在当前位置无法识别有效的语法结构"))) {

            SqlErrorInfo enhancedInfo = AntlrSyntaxTreeAnalyzer.analyzeAntlrError(
                recognizer, offendingSymbol, line, charPositionInLine, msg, e, originalSql);

            // 合并分析结果，保留更详细的信息
            if (enhancedInfo.getFriendlyMessage() != null &&
                !enhancedInfo.getFriendlyMessage().equals(errorInfo.getFriendlyMessage())) {
                errorInfo.setFriendlyMessage(enhancedInfo.getFriendlyMessage());
            }

            if (enhancedInfo.getDetailedMessage() != null) {
                String currentDetail = errorInfo.getDetailedMessage();
                if (currentDetail != null) {
                    errorInfo.setDetailedMessage(currentDetail + "\n\n" + enhancedInfo.getDetailedMessage());
                } else {
                    errorInfo.setDetailedMessage(enhancedInfo.getDetailedMessage());
                }
            }

            if (enhancedInfo.getSuggestion() != null) {
                String currentSuggestion = errorInfo.getSuggestion();
                if (currentSuggestion != null) {
                    errorInfo.setSuggestion(currentSuggestion + "\n\n" + enhancedInfo.getSuggestion());
                } else {
                    errorInfo.setSuggestion(enhancedInfo.getSuggestion());
                }
            }

            log.debug("Enhanced ANTLR analysis completed, merged with pattern matching results");
        }

        return errorInfo;
    }
    
    /**
     * 通用错误分析
     * 当没有匹配的特定错误模式时使用
     */
    private static void analyzeGenericError(String sql, String errorMessage, int line, int position, SqlErrorInfo errorInfo) {
        errorInfo.setErrorType(SqlErrorType.GENERIC_SYNTAX_ERROR);

        // 提供更具体的错误信息
        String friendlyMessage = generateFriendlyErrorMessage(errorMessage);
        errorInfo.setFriendlyMessage(friendlyMessage);

        // 生成详细的错误描述，包含上下文信息
        String detailedMessage = generateDetailedErrorMessage(sql, errorMessage, line, position);
        errorInfo.setDetailedMessage(detailedMessage);

        // 基于错误类型提供具体的修复建议
        String suggestion = generateSuggestion(errorMessage, sql, line, position);
        errorInfo.setSuggestion(suggestion);

        errorInfo.setAutoFixable(false);
    }

    /**
     * 生成友好的错误信息
     */
    private static String generateFriendlyErrorMessage(String errorMessage) {
        if (errorMessage.contains("no viable alternative")) {
            return "语法错误：在当前位置无法识别有效的语法结构";
        } else if (errorMessage.contains("missing")) {
            return "语法错误：缺少必需的语法元素";
        } else if (errorMessage.contains("mismatched input")) {
            return "语法错误：输入的内容与期望的语法不匹配";
        } else if (errorMessage.contains("extraneous input")) {
            return "语法错误：发现多余的输入内容";
        } else {
            return "SQL语法错误";
        }
    }

    /**
     * 生成详细的错误描述
     */
    private static String generateDetailedErrorMessage(String sql, String errorMessage, int line, int position) {
        StringBuilder sb = new StringBuilder();
        sb.append("第").append(line).append("行第").append(position).append("列：").append(errorMessage);

        // 添加错误位置的上下文
        String context = extractErrorContext(sql, line, position);
        if (context != null) {
            sb.append("\n\n错误位置上下文：\n").append(context);
        }

        return sb.toString();
    }

    /**
     * 提取错误位置的上下文
     */
    private static String extractErrorContext(String sql, int line, int position) {
        if (sql == null || sql.isEmpty()) {
            return null;
        }

        String[] lines = sql.split("\n");
        if (line <= 0 || line > lines.length) {
            return null;
        }

        StringBuilder context = new StringBuilder();

        // 显示错误行及其前后行
        int startLine = Math.max(1, line - 1);
        int endLine = Math.min(lines.length, line + 1);

        for (int i = startLine; i <= endLine; i++) {
            String lineContent = lines[i - 1];
            context.append(String.format("%3d: %s\n", i, lineContent));

            // 在错误行下方显示指示符
            if (i == line) {
                context.append("     ");
                for (int j = 0; j < Math.min(position, lineContent.length()); j++) {
                    context.append(" ");
                }
                context.append("^^^ 错误位置\n");
            }
        }

        return context.toString();
    }

    /**
     * 生成修复建议
     */
    private static String generateSuggestion(String errorMessage, String sql, int line, int position) {
        if (errorMessage.contains("no viable alternative")) {
            return "请检查语法结构是否正确。常见问题：1) 关键字拼写错误；2) 缺少必要的标点符号；3) 语法顺序不正确。参考MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/";
        } else if (errorMessage.contains("missing")) {
            if (errorMessage.contains("';'")) {
                return "请在SQL语句末尾添加分号(;)";
            } else if (errorMessage.contains("'('")) {
                return "请检查是否缺少左括号(";
            } else if (errorMessage.contains("')'")) {
                return "请检查是否缺少右括号)";
            } else {
                return "请检查是否缺少必要的语法元素，参考MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/";
            }
        } else if (errorMessage.contains("mismatched input")) {
            return "请检查输入的内容是否符合MySQL语法规范，参考MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/";
        } else {
            return "请检查SQL语法是否符合MySQL标准，参考MySQL官方文档：https://dev.mysql.com/doc/refman/8.4/en/";
        }
    }

    /**
     * 检查SQL是否包含常见的语法问题
     * 委托给 ErrorPatternRegistry 进行检查
     */
    public static boolean hasKnownSyntaxIssues(String sql) {
        return ErrorPatternRegistry.hasKnownSyntaxIssues(sql);
    }

    /**
     * 自动修复已知的语法问题
     * 委托给 ErrorPatternRegistry 进行修复
     */
    public static String autoFixKnownIssues(String sql) {
        return ErrorPatternRegistry.autoFixKnownIssues(sql);
    }

    /**
     * 获取所有支持的错误模式数量
     * 用于统计和调试
     */
    public static int getSupportedPatternCount() {
        return ErrorPatternRegistry.getPatternCount();
    }

    /**
     * 获取所有可自动修复的错误模式数量
     * 用于统计和调试
     */
    public static int getAutoFixablePatternCount() {
        return ErrorPatternRegistry.getAutoFixablePatterns().size();
    }
}
