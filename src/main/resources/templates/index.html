<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL转换工具 - MySQL语句转换器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/themes/prism.min.css" rel="stylesheet">
    <style>
        .sql-textarea {
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }

        .result-section {
            margin-top: 30px;
        }

        .issue-item {
            margin-bottom: 10px;
        }

        .issue-error {
            color: #dc3545;
        }

        .issue-warning {
            color: #ffc107;
        }

        .issue-info {
            color: #17a2b8;
        }

        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
        }

        .code-container {
            position: relative;
        }

        .large-file-notice {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
</head>

<body>
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div class="flex-grow-1 text-center">
                        <h1>
                            <i class="fas fa-database"></i> SQL转换工具
                        </h1>
                        <p class="text-muted">
                            输入MySQL语句，自动生成达梦(Dameng)、金仓(Kingbase)等所有支持数据库的SQL语句
                        </p>
                    </div>
                    <!-- 重置按钮 - 只在有转换结果时显示 -->
                    <div th:if="${transpileResponse != null}">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-redo me-1"></i>
                            重新开始
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 转换表单 -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">SQL转换</h5>
                    </div>
                    <div class="card-body">
                        <form th:action="@{/transpile}" th:object="${transpileRequest}" method="post"
                            enctype="multipart/form-data">
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="sourceDialect" class="form-label">源数据库类型</label>
                                    <select class="form-select" id="sourceDialect" th:field="*{sourceDialect}" readonly>
                                        <option value="mysql">MySQL</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">目标数据库类型</label>
                                    <div class="form-control-plaintext">
                                        <span class="badge bg-success">达梦数据库</span>
                                        <span class="badge bg-success">金仓数据库</span>
                                        <span class="badge bg-success">神通数据库</span>
                                    </div>
                                    <small class="text-muted">将自动转换为所有支持的数据库格式</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="preserveComments"
                                        th:field="*{preserveComments}">
                                    <label class="form-check-label" for="preserveComments">
                                        保留注释语句
                                    </label>
                                </div>
                            </div>

                            <!-- 输入方式选择 -->
                            <div class="mb-3">
                                <label class="form-label">输入方式</label>
                                <div class="btn-group w-100" role="group" aria-label="输入方式选择">
                                    <input type="radio" class="btn-check" name="inputType" id="inputTypeText"
                                        value="text" th:field="*{inputType}" checked>
                                    <label class="btn btn-outline-primary" for="inputTypeText">
                                        <i class="fas fa-keyboard"></i> 文本输入
                                    </label>

                                    <input type="radio" class="btn-check" name="inputType" id="inputTypeFile"
                                        value="file" th:field="*{inputType}">
                                    <label class="btn btn-outline-primary" for="inputTypeFile">
                                        <i class="fas fa-file-upload"></i> 文件上传
                                    </label>
                                </div>
                            </div>

                            <!-- 文本输入区域 -->
                            <div class="mb-3" id="textInputArea">
                                <label for="sql" class="form-label">MySQL SQL语句</label>
                                <textarea class="form-control sql-textarea" id="sql" th:field="*{sql}" rows="10"
                                    placeholder="请输入MySQL SQL语句..."></textarea>

                                <!-- MySQL语法验证问题显示区域 -->
                                <div th:if="${transpileResponse != null and transpileResponse.results != null and not transpileResponse.results.isEmpty()}" class="mt-3" id="mysqlValidationIssues">
                                    <div th:with="firstDbResult=${transpileResponse.results.values().iterator().next()}">
                                        <div th:if="${firstDbResult.issues != null}" th:each="issue : ${firstDbResult.issues}" class="alert mb-2" th:classappend="${issue.level.name() == 'ERROR' ? 'alert-danger' : issue.level.name() == 'WARN' ? 'alert-warning' : 'alert-info'}">
                                            <div class="d-flex align-items-start">
                                                <i th:class="${issue.level.name() == 'ERROR' ? 'fas fa-times-circle text-danger me-2 mt-1' :
                                                              issue.level.name() == 'WARN' ? 'fas fa-exclamation-triangle text-warning me-2 mt-1' :
                                                              'fas fa-info-circle text-info me-2 mt-1'}"></i>
                                                <div class="flex-grow-1">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div>
                                                            <strong th:text="${issue.level.name() == 'ERROR' ? 'MySQL语法错误' :
                                                                             issue.level.name() == 'WARN' ? 'MySQL语法警告' :
                                                                             'MySQL语法建议'}"></strong>
                                                            <small class="text-muted ms-2">基于MySQL 8.4官方文档检测</small>
                                                        </div>
                                                        <!-- 错误位置信息 -->
                                                        <div th:if="${issue.line != null and issue.line > 0}" class="text-muted small">
                                                            <i class="fas fa-map-marker-alt"></i>
                                                            第<span th:text="${issue.line}"></span>行
                                                            <span th:if="${issue.position != null and issue.position > 0}">
                                                                第<span th:text="${issue.position}"></span>列
                                                            </span>
                                                        </div>
                                                    </div>
                                                    <div class="mt-2">
                                                        <div class="error-message" th:text="${issue.message}"></div>
                                                        <!-- 如果错误消息包含上下文信息，使用预格式化显示 -->
                                                        <div th:if="${#strings.contains(issue.message, '错误位置上下文：')}" class="mt-2">
                                                            <small class="text-muted">错误位置：</small>
                                                            <pre class="bg-light p-2 rounded small mt-1" style="font-size: 0.8rem; max-height: 200px; overflow-y: auto;" th:text="${#strings.substringAfter(issue.message, '错误位置上下文：')}"></pre>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 文件上传区域 -->
                            <div class="mb-3" id="fileInputArea" style="display: none;">
                                <label for="sqlFile" class="form-label">选择SQL文件</label>
                                <input type="file" class="form-control" id="sqlFile" name="sqlFile" accept=".sql">
                                <div class="form-text">
                                    <i class="fas fa-info-circle"></i>
                                    支持.sql文件格式，最大文件大小：100MB。<br>
                                    <i class="fas fa-lightbulb"></i>
                                    提示：当SQL文件超过200行时，为了页面性能，系统将只提供下载功能而不显示转换内容。
                                </div>
                            </div>

                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-exchange-alt"></i> 开始转换
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- 错误消息 -->
        <div th:if="${errorMessage}" class="row mt-4">
            <div class="col-12">
                <div class="alert alert-danger" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span th:text="${errorMessage}"></span>
                </div>
            </div>
        </div>

        <!-- 转换结果 -->
        <div th:if="${transpileResponse}" class="result-section">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">转换结果</h5>
                        </div>
                        <div class="card-body">
                            <!-- 全局错误消息 -->
                            <div th:if="${transpileResponse.errorMessage}" class="alert alert-danger mb-4">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span th:text="${transpileResponse.errorMessage}"></span>
                            </div>

                            <!-- 各数据库转换结果 -->
                            <div th:if="${transpileResponse.results}" class="database-results">
                                <div th:each="entry : ${transpileResponse.results}" class="database-result mb-4">
                                    <div class="card">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0">
                                                <i class="fas fa-database me-2"></i>
                                                <span th:text="${entry.value.databaseDescription}"></span>
                                                <small class="text-muted ms-2">
                                                    (共 <span th:text="${entry.value.sqlLineCount}"></span> 行)
                                                </small>
                                            </h6>
                                            <div>
                                                <span class="badge bg-success me-2"
                                                    th:if="${entry.value.successCount > 0}">
                                                    成功: <span th:text="${entry.value.successCount}"></span>
                                                </span>
                                                <span class="badge bg-danger" th:if="${entry.value.failureCount > 0}">
                                                    失败: <span th:text="${entry.value.failureCount}"></span>
                                                </span>
                                                <span class="badge"
                                                    th:classappend="${entry.value.success} ? 'bg-success' : 'bg-danger'">
                                                    <i class="fas" th:classappend="${entry.value.success} ? 'fa-check-circle' : 'fa-times-circle'"></i>
                                                    <span th:text="${entry.value.success} ? '转换成功' : '转换失败'"></span>
                                                </span>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <!-- 转换成功提示 -->
                                            <div th:if="${entry.value.success}" class="alert alert-success mb-3">
                                                <i class="fas fa-check-circle me-2"></i>
                                                <strong>转换完成！</strong>
                                                成功转换了 <strong th:text="${entry.value.successCount}"></strong> 条SQL语句到
                                                <strong th:text="${entry.value.databaseDescription}"></strong>。
                                                <span th:if="${entry.value.failureCount == 0}">所有语句都已成功转换。</span>

                                                <!-- 详细统计信息 -->
                                                <div class="mt-2 small text-muted">
                                                    <i class="fas fa-info-circle me-1"></i>
                                                    <strong>处理统计：</strong>
                                                    原文件共 <span th:text="${entry.value.originalLineCount}"></span> 行，
                                                    识别出 <span th:text="${entry.value.totalStatements}"></span> 条有效SQL语句，
                                                    <span th:if="${entry.value.skippedLines > 0}">
                                                        跳过了 <span th:text="${entry.value.skippedLines}"></span> 行
                                                        （空行、注释行、LOCK/UNLOCK语句等）
                                                    </span>
                                                </div>

                                                <!-- 跳过内容详情 -->
                                                <div th:if="${entry.value.skippedContents != null && !entry.value.skippedContents.empty}" class="mt-3">
                                                    <div class="d-flex gap-2 align-items-center">
                                                        <button class="btn btn-outline-secondary btn-sm" type="button" data-bs-toggle="collapse"
                                                                th:data-bs-target="'#skippedContent-' + ${entry.key.replace(' ', '-').replace('(', '').replace(')', '')}"
                                                                aria-expanded="false">
                                                            <i class="fas fa-eye me-1"></i>
                                                            查看跳过的内容详情 (<span th:text="${#lists.size(entry.value.skippedContents)}"></span> 项)
                                                        </button>
                                                        <button class="btn btn-outline-info btn-sm"
                                                                th:data-key="${entry.key}"
                                                                th:data-name="${entry.value.databaseName}"
                                                                onclick="downloadSkippedContent(this.dataset.key, this.dataset.name)">
                                                            <i class="fas fa-download me-1"></i>
                                                            下载跳过的内容
                                                        </button>
                                                    </div>
                                                    <div class="collapse mt-2" th:id="'skippedContent-' + ${entry.key.replace(' ', '-').replace('(', '').replace(')', '')}">
                                                        <div class="card card-body">
                                                            <h6 class="text-muted mb-2">跳过的内容详情：</h6>
                                                            <div class="table-responsive">
                                                                <table class="table table-sm table-striped">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>类型</th>
                                                                            <th>内容</th>
                                                                            <th>原因</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <tr th:each="skipped : ${entry.value.skippedContents}">
                                                                            <td><span class="badge bg-secondary" th:text="${skipped.type}"></span></td>
                                                                            <td><code class="small" th:text="${skipped.content}"></code></td>
                                                                            <td class="small text-muted" th:text="${skipped.reason}"></td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- 隐藏的跳过内容数据，用于下载 -->
                                                    <div style="display: none;" th:id="'skipped_data_' + ${entry.key}">
                                                        <div th:each="skipped : ${entry.value.skippedContents}">
                                                            <div th:text="'-- ' + ${skipped.type} + ': ' + ${skipped.reason}"></div>
                                                            <div th:text="${skipped.content}"></div>
                                                            <div>-- ----------------------------------------</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 转换失败提示 -->
                                            <div th:if="${!entry.value.success}" class="alert alert-danger mb-3">
                                                <i class="fas fa-exclamation-triangle me-2"></i>
                                                <strong>转换失败！</strong>
                                                <span th:text="${entry.value.errorMessage}"></span>

                                                <!-- 错误详情 -->
                                                <div th:if="${entry.value.errorDetails != null && !entry.value.errorDetails.empty}" class="mt-3">
                                                    <button class="btn btn-outline-danger btn-sm" type="button" data-bs-toggle="collapse"
                                                            th:data-bs-target="'#errorDetails-' + ${entry.key.replace(' ', '-').replace('(', '').replace(')', '')}"
                                                            aria-expanded="false">
                                                        <i class="fas fa-bug me-1"></i>
                                                        查看错误详情 (<span th:text="${#lists.size(entry.value.errorDetails)}"></span> 个错误)
                                                    </button>
                                                    <div class="collapse mt-2" th:id="'errorDetails-' + ${entry.key.replace(' ', '-').replace('(', '').replace(')', '')}">
                                                        <div class="card card-body">
                                                            <h6 class="text-danger mb-2">错误详情：</h6>
                                                            <div class="table-responsive">
                                                                <table class="table table-sm table-striped">
                                                                    <thead>
                                                                        <tr>
                                                                            <th>错误代码</th>
                                                                            <th>级别</th>
                                                                            <th>错误消息</th>
                                                                            <th>出错的SQL</th>
                                                                            <th>行号</th>
                                                                        </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                        <tr th:each="error : ${entry.value.errorDetails}">
                                                                            <td><span class="badge bg-danger" th:text="${error.errorCode}"></span></td>
                                                                            <td><span class="badge bg-warning" th:text="${error.level}"></span></td>
                                                                            <td class="small" th:text="${error.message}"></td>
                                                                            <td><code class="small" th:text="${error.sqlStatement}"></code></td>
                                                                            <td class="text-center" th:text="${error.lineNumber}"></td>
                                                                        </tr>
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 大文件提示 -->
                                            <div th:if="${!entry.value.shouldDisplaySql and entry.value.success}"
                                                class="large-file-notice">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-file-alt fa-2x text-muted me-3"></i>
                                                    <div>
                                                        <h6 class="mb-1">文件较大，已优化显示</h6>
                                                        <p class="mb-0 text-muted">
                                                            由于SQL文件包含 <strong
                                                                th:text="${entry.value.sqlLineCount}"></strong> 行代码，
                                                            为了确保页面加载性能，这里不显示转换后的SQL内容。
                                                            请点击下载按钮获取完整的转换结果。
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 转换后的SQL（仅在shouldDisplaySql为true时显示） -->
                                            <div
                                                th:if="${entry.value.success and entry.value.shouldDisplaySql and not #strings.isEmpty(entry.value.translatedSql)}">
                                                <div class="code-container">
                                                    <button class="btn btn-sm btn-outline-secondary copy-btn"
                                                        th:data-target="'sql_' + ${entry.key}"
                                                        onclick="copyToClipboard(this.dataset.target)">
                                                        <i class="fas fa-copy"></i> 复制
                                                    </button>
                                                    <pre
                                                        class="bg-light p-3 rounded"><code th:id="'sql_' + ${entry.key}"
                                                                                           class="language-sql"
                                                                                           th:text="${entry.value.translatedSql}"></code></pre>
                                                </div>
                                            </div>

                                            <!-- 错误消息 -->
                                            <div th:if="${entry.value.errorMessage}" class="alert alert-warning">
                                                <span th:text="${entry.value.errorMessage}"></span>
                                            </div>

                                            <!-- 问题列表 - 只显示数据库特定的转换问题，不显示MySQL语法验证问题 -->
                                            <div th:if="${entry.value.issues != null and not #lists.isEmpty(entry.value.issues)}"
                                                class="mt-3">
                                                <!-- 过滤掉MySQL语法验证问题，只显示数据库特定的转换问题 -->
                                                <div th:with="conversionIssues=${entry.value.issues.?[not (#strings.contains(message, '根据MySQL') or #strings.contains(message, 'SQL Validation') or #strings.contains(message, '验证') or #strings.contains(message, '语法') or #strings.contains(message, '官方文档')) and (level.name() == 'ERROR' or (level.name() == 'WARN' and not #strings.contains(message, 'generated empty result') and not #strings.contains(message, 'was skipped')))]}"
                                                     th:if="${not #lists.isEmpty(conversionIssues)}">

                                                    <!-- 转换问题 -->
                                                    <div class="mb-3">
                                                        <h6 class="text-danger">
                                                            <i class="fas fa-bug me-2"></i>
                                                            <span th:text="${entry.key}"></span> 转换问题:
                                                        </h6>
                                                        <div th:each="issue : ${conversionIssues}" class="issue-item">
                                                            <div class="alert alert-sm"
                                                                th:classappend="${issue.level.name() == 'ERROR'} ? 'alert-danger' : (${issue.level.name() == 'WARN'} ? 'alert-warning' : 'alert-info')">
                                                                <div class="d-flex align-items-start">
                                                                    <i class="fas me-2 mt-1"
                                                                       th:classappend="${issue.level.name() == 'ERROR'} ? 'fa-times-circle text-danger' : (${issue.level.name() == 'WARN'} ? 'fa-exclamation-triangle text-warning' : 'fa-info-circle text-info')"></i>
                                                                    <div class="flex-grow-1">
                                                                        <strong th:text="${issue.level.name() == 'ERROR'} ? '错误' : (${issue.level.name() == 'WARN'} ? '警告' : '信息')"></strong>
                                                                        [<span th:text="${issue.issueCode}"></span>]:
                                                                        <span th:text="${issue.message}"></span>
                                                                        <span th:if="${issue.line > 0}"> (行: <span th:text="${issue.line}"></span>)</span>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- 下载按钮 -->
                                            <div th:if="${entry.value.success and not #strings.isEmpty(entry.value.translatedSql)}"
                                                class="mt-3">
                                                <button class="btn btn-success btn-sm"
                                                    th:class="${entry.value.shouldDisplaySql} ? 'btn btn-success btn-sm' : 'btn btn-primary'"
                                                    th:data-key="${entry.key}"
                                                    th:data-name="${entry.value.databaseName}"
                                                    th:data-sql="${entry.value.translatedSql}"
                                                    onclick="downloadSql(this)">
                                                    <i class="fas fa-download"></i> 下载 <span
                                                        th:text="${entry.value.databaseName}"></span> SQL
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.24.1/components/prism-sql.min.js"></script>
    <script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>

    <script>
        // 输入方式切换
        function toggleInputType() {
            const textRadio = document.getElementById('inputTypeText');
            const fileRadio = document.getElementById('inputTypeFile');
            const textArea = document.getElementById('textInputArea');
            const fileArea = document.getElementById('fileInputArea');

            if (textRadio.checked) {
                textArea.style.display = 'block';
                fileArea.style.display = 'none';
                // 清空文件输入
                document.getElementById('sqlFile').value = '';
            } else if (fileRadio.checked) {
                textArea.style.display = 'none';
                fileArea.style.display = 'block';
                // 清空文本输入
                document.getElementById('sql').value = '';
            }
        }

        // 复制到剪贴板 - 兼容多种浏览器环境
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            if (!element) {
                console.error('Element not found:', elementId);
                return;
            }

            const text = element.textContent || element.innerText;
            const btn = document.querySelector(`[data-target="${elementId}"]`);

            // 尝试使用现代 Clipboard API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(text).then(function() {
                    showCopySuccess(btn);
                }).catch(function(err) {
                    console.warn('Clipboard API failed, falling back to legacy method:', err);
                    fallbackCopyToClipboard(text, btn);
                });
            } else {
                // 降级到传统方法
                fallbackCopyToClipboard(text, btn);
            }
        }

        // 传统复制方法（兼容性更好）
        function fallbackCopyToClipboard(text, btn) {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);

            try {
                textArea.focus();
                textArea.select();

                // 尝试执行复制命令
                const successful = document.execCommand('copy');
                if (successful) {
                    showCopySuccess(btn);
                } else {
                    showCopyError(btn);
                }
            } catch (err) {
                console.error('Copy failed:', err);
                showCopyError(btn);
            } finally {
                document.body.removeChild(textArea);
            }
        }

        // 显示复制成功
        function showCopySuccess(btn) {
            if (!btn) return;

            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-success');

            setTimeout(function() {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-success');
                btn.classList.add('btn-outline-secondary');
            }, 2000);
        }

        // 显示复制错误
        function showCopyError(btn) {
            if (!btn) return;

            const originalText = btn.innerHTML;
            btn.innerHTML = '<i class="fas fa-exclamation-triangle"></i> 复制失败';
            btn.classList.remove('btn-outline-secondary');
            btn.classList.add('btn-danger');

            setTimeout(function() {
                btn.innerHTML = originalText;
                btn.classList.remove('btn-danger');
                btn.classList.add('btn-outline-secondary');
            }, 2000);

            // 提示用户手动复制
            alert('自动复制失败，请手动选择文本进行复制');
        }

        // 下载SQL文件
        function downloadSql(button) {
            const databaseKey = button.dataset.key;
            const databaseName = button.dataset.name;

            // 优先从DOM元素获取SQL内容，如果不存在则从data属性获取
            let sqlContent;
            const sqlElement = document.getElementById('sql_' + databaseKey);
            if (sqlElement) {
                sqlContent = sqlElement.textContent;
            } else {
                sqlContent = button.dataset.sql;
            }

            if (!sqlContent) {
                alert('无法获取SQL内容');
                return;
            }

            const filename = 'converted_' + databaseKey + '_' + new Date().getTime() + '.sql';

            const blob = new Blob([sqlContent], { type: 'text/plain' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // 下载跳过的内容
        function downloadSkippedContent(databaseKey, databaseName) {
            const skippedDataElement = document.getElementById('skipped_data_' + databaseKey);

            if (!skippedDataElement) {
                alert('没有找到跳过的内容数据');
                return;
            }

            // 构建跳过内容的文件内容
            let content = '-- ========================================\n';
            content += '-- 跳过的内容报告\n';
            content += '-- 数据库: ' + databaseName + '\n';
            content += '-- 生成时间: ' + new Date().toLocaleString() + '\n';
            content += '-- ========================================\n\n';

            // 获取所有跳过的内容
            const skippedItems = skippedDataElement.children;
            let hasContent = false;

            for (let i = 0; i < skippedItems.length; i++) {
                const item = skippedItems[i];
                const text = item.textContent.trim();
                if (text && !text.startsWith('--')) {
                    content += text + '\n\n';
                    hasContent = true;
                } else if (text) {
                    content += text + '\n';
                    hasContent = true;
                }
            }

            if (!hasContent) {
                alert('没有可下载的跳过内容');
                return;
            }

            content += '\n-- ========================================\n';
            content += '-- 报告结束\n';
            content += '-- ========================================\n';

            const filename = 'skipped_content_' + databaseKey + '_' + new Date().getTime() + '.txt';
            const blob = new Blob([content], { type: 'text/plain; charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        }

        // 重置表单
        function resetForm() {
            if (confirm('确定要清空当前结果并重新开始吗？')) {
                // 清空表单数据
                document.getElementById('sql').value = '';
                document.getElementById('sqlFile').value = '';

                // 重置输入方式为文本输入
                document.getElementById('inputTypeText').checked = true;
                document.getElementById('inputTypeFile').checked = false;
                toggleInputType();

                // 重新加载页面，清空所有结果
                window.location.href = '/';
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function () {
            // 语法高亮
            Prism.highlightAll();

            // 绑定输入方式切换事件
            document.getElementById('inputTypeText').addEventListener('change', toggleInputType);
            document.getElementById('inputTypeFile').addEventListener('change', toggleInputType);

            // 初始化输入方式显示
            toggleInputType();

            // 文件选择提示
            document.getElementById('sqlFile').addEventListener('change', function (e) {
                const file = e.target.files[0];
                if (file) {
                    if (!file.name.toLowerCase().endsWith('.sql')) {
                        alert('请选择.sql文件');
                        e.target.value = '';
                        return;
                    }
                    if (file.size > 10 * 1024 * 1024) { // 10MB
                        alert('文件大小不能超过10MB');
                        e.target.value = '';
                        return;
                    }
                }
            });
        });
    </script>
</body>

</html>